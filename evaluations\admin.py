"""
Admin configuration for evaluations app.
"""

from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import EvaluationCategory, EvaluationQuestion, Evaluation, EvaluationResponse


class EvaluationQuestionInline(admin.TabularInline):
    """Inline admin for evaluation questions."""
    model = EvaluationQuestion
    extra = 1
    fields = ['question_en', 'question_ar', 'weight_percentage', 'scale_max', 'order', 'is_active']


@admin.register(EvaluationCategory)
class EvaluationCategoryAdmin(admin.ModelAdmin):
    """Admin for EvaluationCategory model."""
    
    list_display = [
        'name_en', 'name_ar', 'weight_percentage', 
        'is_mandatory', 'is_active', 'order'
    ]
    
    list_filter = ['is_mandatory', 'is_active']
    
    search_fields = ['name_en', 'name_ar', 'description_en', 'description_ar']
    
    ordering = ['order', 'name_en']
    
    fieldsets = [
        (_('Basic Information'), {
            'fields': ('name_en', 'name_ar', 'weight_percentage', 'order')
        }),
        (_('Settings'), {
            'fields': ('is_mandatory', 'is_active')
        }),
        (_('Description'), {
            'fields': ('description_en', 'description_ar'),
            'classes': ['collapse']
        }),
    ]
    
    inlines = [EvaluationQuestionInline]


@admin.register(EvaluationQuestion)
class EvaluationQuestionAdmin(admin.ModelAdmin):
    """Admin for EvaluationQuestion model."""
    
    list_display = [
        'question_en', 'category', 'weight_percentage', 
        'scale_max', 'order', 'is_active'
    ]
    
    list_filter = ['category', 'scale_max', 'is_active']
    
    search_fields = ['question_en', 'question_ar', 'help_text_en', 'help_text_ar']
    
    ordering = ['category__order', 'order']
    
    fieldsets = [
        (_('Question'), {
            'fields': ('category', 'question_en', 'question_ar')
        }),
        (_('Scoring'), {
            'fields': ('weight_percentage', 'scale_max', 'order')
        }),
        (_('Settings'), {
            'fields': ('is_active',)
        }),
        (_('Help Text'), {
            'fields': ('help_text_en', 'help_text_ar'),
            'classes': ['collapse']
        }),
    ]


class EvaluationResponseInline(admin.TabularInline):
    """Inline admin for evaluation responses."""
    model = EvaluationResponse
    extra = 0
    fields = ['question', 'score', 'comments_en']
    readonly_fields = ['question']


@admin.register(Evaluation)
class EvaluationAdmin(admin.ModelAdmin):
    """Admin for Evaluation model."""
    
    list_display = [
        'employee', 'evaluator', 'evaluation_period_start', 
        'evaluation_period_end', 'status', 'percentage_score', 'created_at'
    ]
    
    list_filter = [
        'status', 'evaluation_period_start', 'evaluation_period_end', 
        'created_at', 'evaluator__role'
    ]
    
    search_fields = [
        'employee__english_name', 'employee__arabic_name', 'employee__employee_id',
        'evaluator__english_name', 'evaluator__arabic_name'
    ]
    
    ordering = ['-created_at']
    
    fieldsets = [
        (_('Evaluation Details'), {
            'fields': ('employee', 'evaluator', 'evaluation_period_start', 'evaluation_period_end')
        }),
        (_('Status & Scores'), {
            'fields': ('status', 'total_score', 'percentage_score')
        }),
        (_('Comments'), {
            'fields': ('overall_comments_en', 'overall_comments_ar'),
            'classes': ['collapse']
        }),
        (_('Workflow'), {
            'fields': ('submitted_at', 'approved_at', 'approved_by'),
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['total_score', 'percentage_score', 'submitted_at', 'approved_at']
    
    inlines = [EvaluationResponseInline]
    
    actions = ['calculate_scores']
    
    def calculate_scores(self, request, queryset):
        """Admin action to recalculate scores."""
        for evaluation in queryset:
            evaluation.calculate_score()
        self.message_user(request, _('Scores calculated successfully.'))
    calculate_scores.short_description = _('Recalculate scores')


@admin.register(EvaluationResponse)
class EvaluationResponseAdmin(admin.ModelAdmin):
    """Admin for EvaluationResponse model."""
    
    list_display = [
        'evaluation', 'question', 'score', 'created_at'
    ]
    
    list_filter = [
        'question__category', 'score', 'created_at'
    ]
    
    search_fields = [
        'evaluation__employee__english_name', 
        'evaluation__employee__arabic_name',
        'question__question_en', 'question__question_ar'
    ]
    
    ordering = ['-created_at']
