# Employee Rating System - Core Dependencies
# =============================================================================

# Django Framework
Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1
django-filter==23.3

# Environment Management
python-decouple==3.8

# Database Adapters
# SQLite (built-in with Python)
# PostgreSQL
psycopg2-binary==2.9.7
# Oracle (uncomment if needed)
# cx-Oracle==8.3.0

# Internationalization & Localization
django-modeltranslation==0.18.11
django-rosetta==0.9.9

# Tree Structure for Department Hierarchy
django-mptt==0.14.0

# Authentication & Authorization
django-allauth==0.57.0
djangorestframework-simplejwt==5.3.0

# Caching & Session Management
redis==5.0.1
django-redis==5.4.0

# Background Tasks
celery==5.3.4
django-celery-beat==2.5.0
django-celery-results==2.5.1

# File Handling & Storage
Pillow==10.1.0
django-storages==1.14.2

# API Documentation
drf-spectacular==0.26.5

# Development Tools
django-debug-toolbar==4.2.0
django-extensions==3.2.3

# Data Processing
pandas==2.1.3
openpyxl==3.1.2

# Security
django-ratelimit==4.1.0
django-axes==6.1.1

# Utilities
python-dateutil==2.8.2
pytz==2023.3

# Testing
pytest==7.4.3
pytest-django==4.7.0
factory-boy==3.3.0

# Code Quality
flake8==6.1.0
black==23.11.0
isort==5.12.0

# Production Server
gunicorn==21.2.0
whitenoise==6.6.0
