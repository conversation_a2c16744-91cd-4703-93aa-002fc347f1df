{% extends 'base.html' %}

{% block title %}
    {% if LANGUAGE_CODE == 'ar' %}
        إضافة قسم جديد - نظام تقييم الموظفين
    {% else %}
        Add New Department - Employee Rating System
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid" style="max-width: 800px;">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h2 text-gray-900 mb-1">
                {% if LANGUAGE_CODE == 'ar' %}
                    إضافة قسم جديد
                {% else %}
                    Add New Department
                {% endif %}
            </h1>
            <p class="text-muted">
                {% if LANGUAGE_CODE == 'ar' %}
                    إنشاء قسم جديد في الهيكل التنظيمي
                {% else %}
                    Create a new department in the organizational structure
                {% endif %}
            </p>
        </div>
        <a href="{% url 'departments:list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}العودة{% else %}Back{% endif %}
        </a>
    </div>

    <div class="card">
        <div class="card-body p-4">
            <form method="post">
                {% csrf_token %}
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="name_en" class="form-label fw-semibold">
                            {% if LANGUAGE_CODE == 'ar' %}الاسم بالإنجليزية{% else %}English Name{% endif %}
                            <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="name_en" name="name_en" 
                               placeholder="{% if LANGUAGE_CODE == 'ar' %}أدخل اسم القسم بالإنجليزية{% else %}Enter department name in English{% endif %}" required>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="name_ar" class="form-label fw-semibold">
                            {% if LANGUAGE_CODE == 'ar' %}الاسم بالعربية{% else %}Arabic Name{% endif %}
                            <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="name_ar" name="name_ar" 
                               placeholder="{% if LANGUAGE_CODE == 'ar' %}أدخل اسم القسم بالعربية{% else %}Enter department name in Arabic{% endif %}" required>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="code" class="form-label fw-semibold">
                            {% if LANGUAGE_CODE == 'ar' %}رمز القسم{% else %}Department Code{% endif %}
                            <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="code" name="code" 
                               placeholder="{% if LANGUAGE_CODE == 'ar' %}مثل: IT, HR, SALES{% else %}e.g., IT, HR, SALES{% endif %}" required>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="parent" class="form-label fw-semibold">
                            {% if LANGUAGE_CODE == 'ar' %}القسم الأب{% else %}Parent Department{% endif %}
                        </label>
                        <select class="form-select" id="parent" name="parent">
                            <option value="">
                                {% if LANGUAGE_CODE == 'ar' %}لا يوجد (قسم رئيسي){% else %}None (Root Department){% endif %}
                            </option>
                            {% for dept in departments %}
                            <option value="{{ dept.id }}">{{ dept.name_en }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description_en" class="form-label fw-semibold">
                        {% if LANGUAGE_CODE == 'ar' %}الوصف بالإنجليزية{% else %}English Description{% endif %}
                    </label>
                    <textarea class="form-control" id="description_en" name="description_en" rows="3"
                              placeholder="{% if LANGUAGE_CODE == 'ar' %}وصف القسم ومسؤولياته بالإنجليزية{% else %}Department description and responsibilities in English{% endif %}"></textarea>
                </div>

                <div class="mb-3">
                    <label for="description_ar" class="form-label fw-semibold">
                        {% if LANGUAGE_CODE == 'ar' %}الوصف بالعربية{% else %}Arabic Description{% endif %}
                    </label>
                    <textarea class="form-control" id="description_ar" name="description_ar" rows="3"
                              placeholder="{% if LANGUAGE_CODE == 'ar' %}وصف القسم ومسؤولياته بالعربية{% else %}Department description and responsibilities in Arabic{% endif %}"></textarea>
                </div>

                <div class="mb-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                        <label class="form-check-label fw-semibold" for="is_active">
                            {% if LANGUAGE_CODE == 'ar' %}القسم نشط{% else %}Department Active{% endif %}
                        </label>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{% url 'departments:list' %}" class="btn btn-outline-secondary me-md-2">
                        {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}Cancel{% endif %}
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}إنشاء القسم{% else %}Create Department{% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
