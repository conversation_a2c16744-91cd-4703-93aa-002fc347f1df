{% extends 'base.html' %}

{% block title %}
    {% if LANGUAGE_CODE == 'ar' %}
        إنشاء تقييمات جماعية - نظام تقييم الموظفين
    {% else %}
        Create Bulk Evaluations - Employee Rating System
    {% endif %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h2 text-gray-900 mb-1">
            {% if LANGUAGE_CODE == 'ar' %}إنشاء تقييمات جماعية{% else %}Create Bulk Evaluations{% endif %}
        </h1>
        <p class="text-muted">
            {% if LANGUAGE_CODE == 'ar' %}
                إنشاء تقييمات أداء متعددة للموظفين في نفس الوقت
            {% else %}
                Create multiple performance evaluations for employees at once
            {% endif %}
        </p>
    </div>
    <a href="{% url 'evaluations:list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% if LANGUAGE_CODE == 'ar' %}العودة{% else %}Back{% endif %}
    </a>
</div>

<!-- Step 1: Evaluation Period Selection -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-calendar-alt me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}الخطوة 1: اختيار فترة التقييم{% else %}Step 1: Select Evaluation Period{% endif %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <label for="evaluation_period_start" class="form-label">
                    {% if LANGUAGE_CODE == 'ar' %}بداية فترة التقييم{% else %}Evaluation Period Start{% endif %} *
                </label>
                <input type="date" class="form-control" id="evaluation_period_start" required>
            </div>
            <div class="col-md-4">
                <label for="evaluation_period_end" class="form-label">
                    {% if LANGUAGE_CODE == 'ar' %}نهاية فترة التقييم{% else %}Evaluation Period End{% endif %} *
                </label>
                <input type="date" class="form-control" id="evaluation_period_end" required>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="button" class="btn btn-primary w-100" id="loadEmployeesBtn" disabled>
                    <i class="fas fa-users me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}تحميل الموظفين{% else %}Load Employees{% endif %}
                </button>
            </div>
        </div>
        <div class="mt-3">
            <small class="text-muted">
                {% if LANGUAGE_CODE == 'ar' %}
                    يرجى اختيار فترة التقييم أولاً لتحميل قائمة الموظفين المؤهلين للتقييم
                {% else %}
                    Please select the evaluation period first to load the list of eligible employees
                {% endif %}
            </small>
        </div>
    </div>
</div>

<!-- Step 2: Employee Search and Filter -->
<div class="card shadow mb-4" id="employeeSearchCard" style="display: none;">
    <div class="card-header py-3">
        <div class="d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-search me-2"></i>
                {% if LANGUAGE_CODE == 'ar' %}الخطوة 2: البحث والتصفية{% else %}Step 2: Search & Filter{% endif %}
            </h6>
            <span class="badge bg-info" id="employeeCount">
                {{ total_employees }} {% if LANGUAGE_CODE == 'ar' %}موظف{% else %}employees{% endif %}
            </span>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" id="employeeSearch"
                           placeholder="{% if LANGUAGE_CODE == 'ar' %}البحث بالاسم أو رقم الموظف{% else %}Search by name or employee ID{% endif %}">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="departmentFilter">
                    <option value="">
                        {% if LANGUAGE_CODE == 'ar' %}جميع الأقسام{% else %}All Departments{% endif %}
                    </option>
                    <!-- Department options will be populated dynamically -->
                </select>
            </div>
            <div class="col-md-3">
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-primary" id="selectAllBtn">
                        {% if LANGUAGE_CODE == 'ar' %}تحديد الكل{% else %}Select All{% endif %}
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="clearAllBtn">
                        {% if LANGUAGE_CODE == 'ar' %}إلغاء الكل{% else %}Clear All{% endif %}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Step 3: Bulk Evaluation Form -->
<form method="post" id="bulkEvaluationForm" style="display: none;">
    {% csrf_token %}
    <input type="hidden" name="action" value="bulk_create">
    <input type="hidden" name="evaluation_period_start" id="hiddenStartDate">
    <input type="hidden" name="evaluation_period_end" id="hiddenEndDate">

    <div class="card shadow">
        <div class="card-header py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-clipboard-list me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}الخطوة 3: تقييم الموظفين{% else %}Step 3: Evaluate Employees{% endif %}
                </h6>
                <div>
                    <span class="badge bg-success me-2" id="selectedCount">0 {% if LANGUAGE_CODE == 'ar' %}محدد{% else %}selected{% endif %}</span>
                    <button type="submit" class="btn btn-success" id="submitEvaluationsBtn" disabled>
                        <i class="fas fa-save me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}حفظ التقييمات{% else %}Save Evaluations{% endif %}
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="evaluationTable">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 50px;">
                                <input type="checkbox" class="form-check-input" id="selectAllEmployees">
                            </th>
                            <th style="width: 200px;">
                                {% if LANGUAGE_CODE == 'ar' %}الموظف{% else %}Employee{% endif %}
                            </th>
                            {% for category_data in categories_with_questions %}
                            <th class="text-center" colspan="{{ category_data.questions.count }}">
                                <div class="category-header">
                                    <div class="fw-bold">
                                        {% if LANGUAGE_CODE == 'ar' %}
                                            {{ category_data.category.name_ar }}
                                        {% else %}
                                            {{ category_data.category.name_en }}
                                        {% endif %}
                                    </div>
                                    <small class="text-muted">({{ category_data.category.weight_percentage }}%)</small>
                                </div>
                            </th>
                            {% endfor %}
                            <th class="text-center" style="width: 100px;">
                                {% if LANGUAGE_CODE == 'ar' %}المجموع{% else %}Total{% endif %}
                            </th>
                        </tr>
                        <tr class="table-secondary">
                            <th></th>
                            <th></th>
                            {% for category_data in categories_with_questions %}
                                {% for question in category_data.questions %}
                                <th class="text-center small" style="min-width: 80px;">
                                    <div class="question-header" title="{% if LANGUAGE_CODE == 'ar' %}{{ question.question_ar }}{% else %}{{ question.question_en }}{% endif %}">
                                        {% if LANGUAGE_CODE == 'ar' %}
                                            {{ question.question_ar|truncatechars:20 }}
                                        {% else %}
                                            {{ question.question_en|truncatechars:20 }}
                                        {% endif %}
                                        <br><small class="text-muted">({{ question.weight_percentage }}%)</small>
                                    </div>
                                </th>
                                {% endfor %}
                            {% endfor %}
                            <th></th>
                        </tr>
                    </thead>
                    <tbody id="employeeTableBody">
                        {% for employee in employees %}
                        <tr class="employee-row" data-employee-id="{{ employee.id }}"
                            data-employee-name="{% if LANGUAGE_CODE == 'ar' %}{{ employee.arabic_name|default:employee.english_name }}{% else %}{{ employee.english_name }}{% endif %}"
                            data-employee-id-number="{{ employee.employee_id }}">
                            <td>
                                <input type="checkbox" class="form-check-input employee-checkbox"
                                       name="employee_{{ employee.id }}_selected"
                                       data-employee-id="{{ employee.id }}">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar me-3">
                                        <div class="avatar-initial bg-primary rounded-circle">
                                            {{ employee.english_name|first }}
                                        </div>
                                    </div>
                                    <div>
                                        <div class="fw-bold">
                                            {% if LANGUAGE_CODE == 'ar' %}
                                                {{ employee.arabic_name|default:employee.english_name }}
                                            {% else %}
                                                {{ employee.english_name }}
                                            {% endif %}
                                        </div>
                                        <small class="text-muted">{{ employee.employee_id }}</small>
                                    </div>
                                </div>
                            </td>
                            {% for category_data in categories_with_questions %}
                                {% for question in category_data.questions %}
                                <td class="text-center">
                                    <input type="number" class="form-control form-control-sm score-input"
                                           name="employee_{{ employee.id }}_question_{{ question.id }}"
                                           min="0" step="0.1"
                                           data-employee-id="{{ employee.id }}"
                                           data-question-id="{{ question.id }}"
                                           data-category-weight="{{ question.category.weight_percentage }}"
                                           data-question-weight="{{ question.weight_percentage }}"
                                           style="width: 70px;" disabled>
                                </td>
                                {% endfor %}
                            {% endfor %}
                            <td class="text-center">
                                <div class="total-score fw-bold text-primary" data-employee-id="{{ employee.id }}">-</div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</form>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startDateInput = document.getElementById('evaluation_period_start');
    const endDateInput = document.getElementById('evaluation_period_end');
    const loadEmployeesBtn = document.getElementById('loadEmployeesBtn');
    const employeeSearchCard = document.getElementById('employeeSearchCard');
    const bulkEvaluationForm = document.getElementById('bulkEvaluationForm');
    const employeeSearch = document.getElementById('employeeSearch');
    const selectAllBtn = document.getElementById('selectAllBtn');
    const clearAllBtn = document.getElementById('clearAllBtn');
    const selectAllEmployees = document.getElementById('selectAllEmployees');
    const submitEvaluationsBtn = document.getElementById('submitEvaluationsBtn');
    const selectedCount = document.getElementById('selectedCount');

    // Enable load button when both dates are selected
    function checkDates() {
        if (startDateInput.value && endDateInput.value) {
            loadEmployeesBtn.disabled = false;
        } else {
            loadEmployeesBtn.disabled = true;
        }
    }

    startDateInput.addEventListener('change', checkDates);
    endDateInput.addEventListener('change', checkDates);

    // Load employees and show evaluation form
    loadEmployeesBtn.addEventListener('click', function() {
        if (!startDateInput.value || !endDateInput.value) {
            alert('{% if LANGUAGE_CODE == "ar" %}يرجى اختيار تواريخ فترة التقييم{% else %}Please select evaluation period dates{% endif %}');
            return;
        }

        // Set hidden form values
        document.getElementById('hiddenStartDate').value = startDateInput.value;
        document.getElementById('hiddenEndDate').value = endDateInput.value;

        // Show employee search and evaluation form
        employeeSearchCard.style.display = 'block';
        bulkEvaluationForm.style.display = 'block';

        // Scroll to employee section
        employeeSearchCard.scrollIntoView({ behavior: 'smooth' });
    });

    // Employee search functionality
    employeeSearch.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = document.querySelectorAll('.employee-row');

        rows.forEach(row => {
            const employeeName = row.dataset.employeeName.toLowerCase();
            const employeeId = row.dataset.employeeIdNumber.toLowerCase();

            if (employeeName.includes(searchTerm) || employeeId.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });

    // Select all functionality
    selectAllBtn.addEventListener('click', function() {
        const visibleCheckboxes = document.querySelectorAll('.employee-row:not([style*="display: none"]) .employee-checkbox');
        visibleCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
            toggleEmployeeInputs(checkbox.dataset.employeeId, true);
        });
        updateSelectedCount();
    });

    // Clear all functionality
    clearAllBtn.addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('.employee-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
            toggleEmployeeInputs(checkbox.dataset.employeeId, false);
        });
        updateSelectedCount();
    });

    // Select all employees checkbox
    selectAllEmployees.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.employee-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
            toggleEmployeeInputs(checkbox.dataset.employeeId, this.checked);
        });
        updateSelectedCount();
    });

    // Individual employee checkbox handling
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('employee-checkbox')) {
            const employeeId = e.target.dataset.employeeId;
            const isChecked = e.target.checked;

            toggleEmployeeInputs(employeeId, isChecked);
            updateSelectedCount();
        }
    });

    // Score input handling for automatic calculation
    document.addEventListener('input', function(e) {
        if (e.target.classList.contains('score-input')) {
            const employeeId = e.target.dataset.employeeId;
            calculateEmployeeTotal(employeeId);
        }
    });

    // Toggle employee score inputs
    function toggleEmployeeInputs(employeeId, enabled) {
        const inputs = document.querySelectorAll(`input[data-employee-id="${employeeId}"].score-input`);
        inputs.forEach(input => {
            input.disabled = !enabled;
            if (!enabled) {
                input.value = '';
            }
        });

        if (!enabled) {
            const totalElement = document.querySelector(`.total-score[data-employee-id="${employeeId}"]`);
            if (totalElement) {
                totalElement.textContent = '-';
            }
        }
    }

    // Calculate total score for an employee
    function calculateEmployeeTotal(employeeId) {
        const inputs = document.querySelectorAll(`input[data-employee-id="${employeeId}"].score-input`);
        let totalScore = 0;
        let totalWeight = 0;
        let hasValues = false;

        inputs.forEach(input => {
            const score = parseFloat(input.value);
            if (!isNaN(score) && score >= 0 && score <= 10) {  // Allow 0-10 range for more flexibility
                const categoryWeight = parseFloat(input.dataset.categoryWeight) / 100;
                const questionWeight = parseFloat(input.dataset.questionWeight) / 100;
                const weightedScore = score * categoryWeight * questionWeight;

                totalScore += weightedScore;
                totalWeight += categoryWeight * questionWeight;
                hasValues = true;
            }
        });

        const totalElement = document.querySelector(`.total-score[data-employee-id="${employeeId}"]`);
        if (totalElement) {
            if (hasValues && totalWeight > 0) {
                const percentage = (totalScore / (10 * totalWeight)) * 100;  // Updated to use 10 as max score
                totalElement.textContent = percentage.toFixed(1) + '%';
                totalElement.className = 'total-score fw-bold ' + getScoreClass(percentage);
            } else {
                totalElement.textContent = '-';
                totalElement.className = 'total-score fw-bold text-primary';
            }
        }
    }

    // Get CSS class based on score
    function getScoreClass(percentage) {
        if (percentage >= 90) return 'text-success';
        if (percentage >= 80) return 'text-info';
        if (percentage >= 70) return 'text-warning';
        return 'text-danger';
    }

    // Update selected count and submit button state
    function updateSelectedCount() {
        const checkedBoxes = document.querySelectorAll('.employee-checkbox:checked');
        const count = checkedBoxes.length;

        selectedCount.textContent = count + ' {% if LANGUAGE_CODE == "ar" %}محدد{% else %}selected{% endif %}';
        submitEvaluationsBtn.disabled = count === 0;

        // Update select all checkbox state
        const totalCheckboxes = document.querySelectorAll('.employee-checkbox');
        if (count === 0) {
            selectAllEmployees.indeterminate = false;
            selectAllEmployees.checked = false;
        } else if (count === totalCheckboxes.length) {
            selectAllEmployees.indeterminate = false;
            selectAllEmployees.checked = true;
        } else {
            selectAllEmployees.indeterminate = true;
        }
    }

    // Form submission validation
    bulkEvaluationForm.addEventListener('submit', function(e) {
        const checkedBoxes = document.querySelectorAll('.employee-checkbox:checked');

        if (checkedBoxes.length === 0) {
            e.preventDefault();
            alert('{% if LANGUAGE_CODE == "ar" %}يرجى اختيار موظف واحد على الأقل للتقييم{% else %}Please select at least one employee to evaluate{% endif %}');
            return;
        }

        // Validate that selected employees have scores
        let hasIncompleteEvaluations = false;
        checkedBoxes.forEach(checkbox => {
            const employeeId = checkbox.dataset.employeeId;
            const inputs = document.querySelectorAll(`input[data-employee-id="${employeeId}"].score-input`);
            let hasAnyScore = false;

            inputs.forEach(input => {
                if (input.value && !isNaN(parseFloat(input.value))) {
                    hasAnyScore = true;
                }
            });

            if (!hasAnyScore) {
                hasIncompleteEvaluations = true;
            }
        });

        if (hasIncompleteEvaluations) {
            const confirmMessage = '{% if LANGUAGE_CODE == "ar" %}بعض الموظفين المحددين لا يحتوون على درجات. هل تريد المتابعة؟{% else %}Some selected employees have no scores. Do you want to continue?{% endif %}';
            if (!confirm(confirmMessage)) {
                e.preventDefault();
                return;
            }
        }

        // Show loading state
        submitEvaluationsBtn.disabled = true;
        submitEvaluationsBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{% if LANGUAGE_CODE == "ar" %}جاري الحفظ...{% else %}Saving...{% endif %}';
    });
});
</script>

<style>
.avatar {
    width: 40px;
    height: 40px;
}

.avatar-initial {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
}

.category-header {
    padding: 8px;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.question-header {
    font-size: 0.75rem;
    line-height: 1.2;
    padding: 4px;
}

.score-input:disabled {
    background-color: #f8f9fa;
    opacity: 0.6;
}

.employee-row:hover {
    background-color: #f8f9fa;
}

.table th {
    border-top: none;
    vertical-align: middle;
}

.table td {
    vertical-align: middle;
}

/* RTL Support */
{% if LANGUAGE_CODE == 'ar' %}
.table th, .table td {
    text-align: right;
}

.table th.text-center, .table td.text-center {
    text-align: center !important;
}
{% endif %}

/* Responsive table */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .question-header {
        font-size: 0.7rem;
    }

    .score-input {
        width: 60px !important;
    }
}
</style>
{% endblock %}
