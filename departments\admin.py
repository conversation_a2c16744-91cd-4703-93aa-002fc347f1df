"""
Admin configuration for departments app.
"""

from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from mptt.admin import MPTTModelAdmin
from .models import Department, DepartmentHierarchyView


@admin.register(Department)
class DepartmentAdmin(MPTTModelAdmin):
    """Admin for Department model with MPTT tree structure."""
    
    list_display = [
        'name_en', 'name_ar', 'code', 'parent',
        'is_active', 'created_at'
    ]
    
    list_filter = ['is_active', 'created_at', 'level']
    
    search_fields = ['name_en', 'name_ar', 'code', 'description_en', 'description_ar']
    
    ordering = ['tree_id', 'lft']
    
    fieldsets = [
        (_('Basic Information'), {
            'fields': ('name_en', 'name_ar', 'code', 'parent')
        }),
        (_('Description'), {
            'fields': ('description_en', 'description_ar'),
            'classes': ['collapse']
        }),
        (_('Management'), {
            'fields': ('managers', 'is_active')
        }),
    ]
    
    filter_horizontal = ['managers']
    
    # Temporarily removed employee count display until department relationships are implemented
    
    def get_queryset(self, request):
        """Optimize queryset for admin list view."""
        return super().get_queryset(request).select_related('parent')


@admin.register(DepartmentHierarchyView)
class DepartmentHierarchyViewAdmin(admin.ModelAdmin):
    """Admin for DepartmentHierarchyView model."""
    
    list_display = [
        'department', 'level', 'path', 'total_employees', 
        'direct_employees', 'last_updated'
    ]
    
    list_filter = ['level', 'last_updated']
    
    search_fields = ['department__name_en', 'department__name_ar', 'path', 'path_ar']
    
    readonly_fields = [
        'department', 'level', 'path', 'path_ar', 
        'total_employees', 'direct_employees', 'last_updated'
    ]
    
    ordering = ['level', 'department__name_en']
    
    actions = ['refresh_hierarchy_views']
    
    def refresh_hierarchy_views(self, request, queryset):
        """Admin action to refresh hierarchy views."""
        DepartmentHierarchyView.refresh_all()
        self.message_user(request, _('Hierarchy views refreshed successfully.'))
    refresh_hierarchy_views.short_description = _('Refresh hierarchy views')
