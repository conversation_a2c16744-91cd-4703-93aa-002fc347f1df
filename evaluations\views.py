"""
Views for evaluations app.
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.views.decorators.cache import never_cache
from django.views.decorators.vary import vary_on_headers
from .models import Evaluation, EvaluationCategory


@never_cache
@vary_on_headers('User-Agent')
@login_required
def evaluation_list(request):
    """List evaluations accessible to the user."""
    user = request.user
    evaluations = []

    # Filter evaluations based on user role
    # Note: Using Evaluation.objects automatically excludes soft-deleted records
    if user.role == 'SUPER_ADMIN':
        evaluations = Evaluation.objects.all()
    elif user.role in ['MANAGER', 'SUPERVISOR']:
        # Get evaluations for employees they can evaluate
        accessible_employees = []
        if user.role == 'MANAGER':
            accessible_depts = user.get_accessible_departments()
            from users.models import CustomUser
            accessible_employees = CustomUser.objects.filter(
                primary_department__in=accessible_depts,
                is_deleted=False  # Also exclude soft-deleted users
            )
        elif user.role == 'SUPERVISOR':
            accessible_employees = user.primary_department.get_direct_employees().filter(is_deleted=False)

        evaluations = Evaluation.objects.filter(employee__in=accessible_employees)
    elif user.role == 'QUALITY_TEAM':
        evaluations = Evaluation.objects.all()  # Read-only access
    else:  # EMPLOYEE
        evaluations = Evaluation.objects.filter(employee=user)

    # Order by most recent first and optimize queries
    evaluations = evaluations.select_related('employee', 'evaluator').order_by('-created_at')

    return render(request, 'evaluations/list.html', {
        'evaluations': evaluations,
        'user_role': user.role
    })


@login_required
def evaluation_detail(request, pk):
    """Evaluation detail view with access control."""
    evaluation = get_object_or_404(Evaluation, pk=pk)
    
    # Check access permissions
    user = request.user
    can_view = False
    
    if user.role == 'SUPER_ADMIN':
        can_view = True
    elif user.role == 'QUALITY_TEAM':
        can_view = True  # Read-only access
    elif user.role in ['MANAGER', 'SUPERVISOR']:
        can_view = user.can_evaluate_user(evaluation.employee)
    elif user.role == 'EMPLOYEE':
        can_view = evaluation.employee == user
    
    if not can_view:
        from django.core.exceptions import PermissionDenied
        raise PermissionDenied(_('You do not have permission to view this evaluation.'))
    
    # Get evaluation responses grouped by category
    categories = EvaluationCategory.objects.filter(is_active=True).order_by('order')
    category_data = []
    
    for category in categories:
        responses = evaluation.responses.filter(question__category=category)
        category_score = evaluation.get_category_score(category)
        category_data.append({
            'category': category,
            'responses': responses,
            'score': category_score
        })
    
    context = {
        'evaluation': evaluation,
        'category_data': category_data,
        'can_edit': user.role in ['SUPER_ADMIN', 'MANAGER', 'SUPERVISOR'] and evaluation.status == 'DRAFT',
        'user_role': user.role
    }
    
    return render(request, 'evaluations/detail.html', context)


@login_required
def evaluation_create(request, employee_id=None):
    """Create bulk evaluations for multiple employees."""
    from users.models import CustomUser
    from evaluations.models import EvaluationQuestion, EvaluationResponse
    from datetime import datetime

    # Check permissions
    if request.user.role not in ['SUPER_ADMIN', 'MANAGER', 'SUPERVISOR']:
        messages.error(request, _('You do not have permission to create evaluations.'))
        return redirect('evaluations:list')

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'bulk_create':
            # Handle bulk evaluation creation
            evaluation_period_start = request.POST.get('evaluation_period_start')
            evaluation_period_end = request.POST.get('evaluation_period_end')

            if not evaluation_period_start or not evaluation_period_end:
                messages.error(request, _('Please select both start and end dates for the evaluation period.'))
                return redirect('evaluations:create')

            # Get selected employees and their scores
            created_count = 0
            error_count = 0

            # Get all employees that were submitted
            for key, value in request.POST.items():
                if key.startswith('employee_') and key.endswith('_selected') and value == 'on':
                    employee_id = key.replace('employee_', '').replace('_selected', '')

                    try:
                        employee = CustomUser.objects.get(pk=employee_id)

                        # Create evaluation
                        evaluation = Evaluation.objects.create(
                            employee=employee,
                            evaluator=request.user,
                            evaluation_period_start=evaluation_period_start,
                            evaluation_period_end=evaluation_period_end,
                            status='DRAFT'
                        )

                        # Save evaluation responses
                        total_score = 0
                        total_weight = 0

                        for question in EvaluationQuestion.objects.filter(is_active=True):
                            score_key = f'employee_{employee_id}_question_{question.id}'
                            score = request.POST.get(score_key)

                            if score and score.strip():
                                try:
                                    score_value = float(score)
                                    if 0 <= score_value <= 10:  # Allow 0-10 range for more flexibility
                                        EvaluationResponse.objects.create(
                                            evaluation=evaluation,
                                            question=question,
                                            score=score_value
                                        )

                                        # Calculate weighted score
                                        category_weight = question.category.weight_percentage / 100
                                        question_weight = question.weight_percentage / 100
                                        weighted_score = score_value * category_weight * question_weight
                                        total_score += weighted_score
                                        total_weight += category_weight * question_weight

                                except ValueError:
                                    pass  # Skip invalid scores

                        # Update evaluation total score
                        if total_weight > 0:
                            evaluation.total_score = total_score
                            evaluation.percentage_score = (total_score / (10 * total_weight)) * 100  # Updated to use 10 as max score
                            evaluation.save()

                        created_count += 1

                    except CustomUser.DoesNotExist:
                        error_count += 1
                        continue
                    except Exception as e:
                        error_count += 1
                        continue

            if created_count > 0:
                messages.success(request, _('Successfully created {} evaluations.').format(created_count))
            if error_count > 0:
                messages.warning(request, _('Failed to create {} evaluations.').format(error_count))

            return redirect('evaluations:list')

    # Get employees that can be evaluated
    employees = CustomUser.objects.none()
    if request.user.role == 'SUPER_ADMIN':
        employees = CustomUser.objects.filter(
            role__in=['MANAGER', 'SUPERVISOR', 'EMPLOYEE'],
            is_active=True
        ).order_by('english_name')
    elif request.user.role == 'MANAGER':
        employees = CustomUser.objects.filter(
            role__in=['SUPERVISOR', 'EMPLOYEE'],
            is_active=True
        ).order_by('english_name')
    elif request.user.role == 'SUPERVISOR':
        employees = CustomUser.objects.filter(
            role='EMPLOYEE',
            is_active=True
        ).order_by('english_name')

    # Get active evaluation categories and questions
    categories = EvaluationCategory.objects.filter(is_active=True).order_by('order')
    questions = EvaluationQuestion.objects.filter(is_active=True).order_by('category__order', 'order')

    # Group questions by category for better organization
    categories_with_questions = []
    for category in categories:
        category_questions = questions.filter(category=category)
        categories_with_questions.append({
            'category': category,
            'questions': category_questions
        })

    context = {
        'employees': employees,
        'categories': categories,
        'questions': questions,
        'categories_with_questions': categories_with_questions,
        'total_employees': employees.count(),
    }

    return render(request, 'evaluations/create.html', context)


@login_required
def evaluation_edit(request, pk):
    """Edit existing evaluation."""
    evaluation = get_object_or_404(Evaluation, pk=pk)
    
    # Check permissions
    if not request.user.can_evaluate_user(evaluation.employee):
        from django.core.exceptions import PermissionDenied
        raise PermissionDenied(_('You do not have permission to edit this evaluation.'))
    
    if evaluation.status != 'DRAFT':
        messages.error(request, _('Only draft evaluations can be edited.'))
        return redirect('evaluations:detail', pk=pk)
    
    if request.method == 'POST':
        # Handle evaluation update logic here
        messages.success(request, _('Evaluation updated successfully.'))
        return redirect('evaluations:detail', pk=pk)
    
    # Get evaluation data for editing
    categories = EvaluationCategory.objects.filter(is_active=True).order_by('order')
    
    context = {
        'evaluation': evaluation,
        'categories': categories,
    }
    
    return render(request, 'evaluations/edit.html', context)
