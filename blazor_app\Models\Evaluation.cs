using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmployeeRatingSystem.Blazor.Models
{
    /// <summary>
    /// Individual employee evaluation instance.
    /// Equivalent to Django's Evaluation model.
    /// </summary>
    public class Evaluation : BaseModel
    {
        /// <summary>
        /// Employee ID (who is being evaluated)
        /// </summary>
        [Required]
        public string EmployeeId { get; set; } = string.Empty;

        /// <summary>
        /// Employee navigation property
        /// </summary>
        public virtual ApplicationUser Employee { get; set; } = null!;

        /// <summary>
        /// Evaluator ID (who is doing the evaluation)
        /// </summary>
        [Required]
        public string EvaluatorId { get; set; } = string.Empty;

        /// <summary>
        /// Evaluator navigation property
        /// </summary>
        public virtual ApplicationUser Evaluator { get; set; } = null!;

        /// <summary>
        /// Start date of evaluation period
        /// </summary>
        [Required]
        [Display(Name = "Evaluation Period Start")]
        public DateTime EvaluationPeriodStart { get; set; }

        /// <summary>
        /// End date of evaluation period
        /// </summary>
        [Required]
        [Display(Name = "Evaluation Period End")]
        public DateTime EvaluationPeriodEnd { get; set; }

        /// <summary>
        /// Status of the evaluation
        /// </summary>
        [Required]
        [Display(Name = "Status")]
        public EvaluationStatus Status { get; set; } = EvaluationStatus.DRAFT;

        /// <summary>
        /// Calculated total score
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "Total Score")]
        public decimal? TotalScore { get; set; }

        /// <summary>
        /// Score as percentage
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "Percentage Score")]
        public decimal? PercentageScore { get; set; }

        /// <summary>
        /// General comments about performance in English
        /// </summary>
        [Display(Name = "Overall Comments (English)")]
        public string OverallCommentsEn { get; set; } = string.Empty;

        /// <summary>
        /// General comments about performance in Arabic
        /// </summary>
        [Display(Name = "Overall Comments (Arabic)")]
        public string OverallCommentsAr { get; set; } = string.Empty;

        /// <summary>
        /// Date when evaluation was submitted
        /// </summary>
        [Display(Name = "Submitted At")]
        public DateTime? SubmittedAt { get; set; }

        /// <summary>
        /// Date when evaluation was approved
        /// </summary>
        [Display(Name = "Approved At")]
        public DateTime? ApprovedAt { get; set; }

        /// <summary>
        /// User ID who approved the evaluation
        /// </summary>
        public string? ApprovedById { get; set; }

        /// <summary>
        /// User who approved the evaluation
        /// </summary>
        public virtual ApplicationUser? ApprovedBy { get; set; }

        /// <summary>
        /// Date when evaluation was rejected
        /// </summary>
        [Display(Name = "Rejected At")]
        public DateTime? RejectedAt { get; set; }

        /// <summary>
        /// User ID who rejected the evaluation
        /// </summary>
        public string? RejectedById { get; set; }

        /// <summary>
        /// User who rejected the evaluation
        /// </summary>
        public virtual ApplicationUser? RejectedBy { get; set; }

        /// <summary>
        /// Rejection reason
        /// </summary>
        [StringLength(1000)]
        [Display(Name = "Rejection Reason")]
        public string? RejectionReason { get; set; }

        /// <summary>
        /// Responses to evaluation questions
        /// </summary>
        public virtual ICollection<EvaluationResponse> Responses { get; set; } = new List<EvaluationResponse>();

        /// <summary>
        /// Calculate total score based on category weights and responses
        /// </summary>
        public void CalculateScore()
        {
            decimal totalWeightedScore = 0;
            decimal totalWeight = 0;

            // Group responses by category
            var responsesByCategory = Responses
                .Where(r => !r.IsDeleted && r.Question.IsActive && r.Question.Category.IsActive)
                .GroupBy(r => r.Question.Category);

            foreach (var categoryGroup in responsesByCategory)
            {
                var category = categoryGroup.Key;
                decimal categoryScore = GetCategoryScore(category);

                if (categoryScore >= 0)
                {
                    decimal weightedScore = categoryScore * (category.WeightPercentage / 100);
                    totalWeightedScore += weightedScore;
                    totalWeight += category.WeightPercentage;
                }
            }

            if (totalWeight > 0)
            {
                // Normalize to account for any missing categories
                TotalScore = (totalWeightedScore / totalWeight) * 100;
                PercentageScore = TotalScore;
            }
            else
            {
                TotalScore = 0;
                PercentageScore = 0;
            }
        }

        /// <summary>
        /// Get score for a specific category
        /// </summary>
        public decimal GetCategoryScore(EvaluationCategory category)
        {
            var categoryResponses = Responses
                .Where(r => !r.IsDeleted && r.Question.CategoryId == category.Id && r.Question.IsActive)
                .ToList();

            if (!categoryResponses.Any())
            {
                return -1; // No responses for this category
            }

            decimal totalWeightedScore = 0;
            decimal totalWeight = 0;

            foreach (var response in categoryResponses)
            {
                var question = response.Question;
                decimal normalizedScore = (response.Score / question.MaxScore) * 100;
                totalWeightedScore += normalizedScore * (question.WeightPercentage / 100);
                totalWeight += question.WeightPercentage;
            }

            if (totalWeight > 0)
            {
                return (totalWeightedScore / totalWeight) * 100;
            }

            return 0;
        }

        /// <summary>
        /// Submit the evaluation
        /// </summary>
        public void Submit()
        {
            if (Status == EvaluationStatus.DRAFT)
            {
                CalculateScore();
                Status = EvaluationStatus.SUBMITTED;
                SubmittedAt = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Approve the evaluation
        /// </summary>
        public void Approve(ApplicationUser approver)
        {
            if (Status == EvaluationStatus.SUBMITTED)
            {
                Status = EvaluationStatus.APPROVED;
                ApprovedAt = DateTime.UtcNow;
                ApprovedById = approver.Id;
                ApprovedBy = approver;
            }
        }

        /// <summary>
        /// Reject the evaluation
        /// </summary>
        public void Reject(ApplicationUser rejector, string reason)
        {
            if (Status == EvaluationStatus.SUBMITTED)
            {
                Status = EvaluationStatus.REJECTED;
                RejectedAt = DateTime.UtcNow;
                RejectedById = rejector.Id;
                RejectedBy = rejector;
                RejectionReason = reason;
            }
        }

        /// <summary>
        /// Get overall comments based on language preference
        /// </summary>
        public string GetOverallComments(string language = "en")
        {
            return language == "ar" ? OverallCommentsAr : OverallCommentsEn;
        }

        public override string ToString()
        {
            return $"Evaluation of {Employee?.EnglishName} by {Evaluator?.EnglishName} ({Status})";
        }
    }
}
