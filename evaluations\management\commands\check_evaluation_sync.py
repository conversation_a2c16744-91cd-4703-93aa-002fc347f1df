"""
Management command to check and fix evaluation data synchronization issues.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from evaluations.models import Evaluation
from users.models import CustomUser


class Command(BaseCommand):
    help = 'Check and fix evaluation data synchronization issues'

    def add_arguments(self, parser):
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Actually fix the issues (dry run by default)',
        )
        parser.add_argument(
            '--show-deleted',
            action='store_true',
            help='Show soft-deleted evaluations',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Checking evaluation data synchronization...')
        )

        # Check all evaluations (including soft-deleted)
        all_evaluations = Evaluation.all_objects.all()
        active_evaluations = Evaluation.objects.all()
        
        self.stdout.write(f"Total evaluations in database: {all_evaluations.count()}")
        self.stdout.write(f"Active evaluations: {active_evaluations.count()}")
        self.stdout.write(f"Soft-deleted evaluations: {all_evaluations.count() - active_evaluations.count()}")

        if options['show_deleted']:
            self.stdout.write("\n" + "="*50)
            self.stdout.write("SOFT-DELETED EVALUATIONS:")
            self.stdout.write("="*50)
            
            deleted_evaluations = Evaluation.all_objects.filter(is_deleted=True)
            for eval in deleted_evaluations:
                self.stdout.write(
                    f"ID: {eval.id} | Employee: {eval.employee.english_name} | "
                    f"Status: {eval.status} | Deleted: {eval.deleted_at}"
                )

        # Check for orphaned evaluations (employee or evaluator deleted)
        self.stdout.write("\n" + "="*50)
        self.stdout.write("CHECKING FOR ORPHANED EVALUATIONS:")
        self.stdout.write("="*50)
        
        orphaned_count = 0
        for eval in active_evaluations:
            issues = []
            
            # Check if employee is soft-deleted
            if eval.employee.is_deleted:
                issues.append("Employee is soft-deleted")
            
            # Check if evaluator is soft-deleted
            if eval.evaluator.is_deleted:
                issues.append("Evaluator is soft-deleted")
            
            if issues:
                orphaned_count += 1
                self.stdout.write(
                    self.style.WARNING(
                        f"Evaluation ID {eval.id}: {', '.join(issues)}"
                    )
                )
                
                if options['fix']:
                    eval.soft_delete()
                    self.stdout.write(
                        self.style.SUCCESS(f"  → Fixed: Soft-deleted evaluation {eval.id}")
                    )

        if orphaned_count == 0:
            self.stdout.write(self.style.SUCCESS("No orphaned evaluations found."))
        else:
            if options['fix']:
                self.stdout.write(
                    self.style.SUCCESS(f"Fixed {orphaned_count} orphaned evaluations.")
                )
            else:
                self.stdout.write(
                    self.style.WARNING(
                        f"Found {orphaned_count} orphaned evaluations. "
                        "Use --fix to resolve them."
                    )
                )

        # Check for evaluations with invalid status
        self.stdout.write("\n" + "="*50)
        self.stdout.write("CHECKING FOR INVALID STATUS:")
        self.stdout.write("="*50)
        
        valid_statuses = [choice[0] for choice in Evaluation.EVALUATION_STATUS]
        invalid_status_count = 0
        
        for eval in active_evaluations:
            if eval.status not in valid_statuses:
                invalid_status_count += 1
                self.stdout.write(
                    self.style.ERROR(
                        f"Evaluation ID {eval.id} has invalid status: {eval.status}"
                    )
                )
                
                if options['fix']:
                    eval.status = 'DRAFT'
                    eval.save()
                    self.stdout.write(
                        self.style.SUCCESS(f"  → Fixed: Reset status to DRAFT")
                    )

        if invalid_status_count == 0:
            self.stdout.write(self.style.SUCCESS("No invalid statuses found."))

        # Summary
        self.stdout.write("\n" + "="*50)
        self.stdout.write("SUMMARY:")
        self.stdout.write("="*50)
        self.stdout.write(f"Total evaluations: {all_evaluations.count()}")
        self.stdout.write(f"Active evaluations: {active_evaluations.count()}")
        self.stdout.write(f"Soft-deleted evaluations: {all_evaluations.count() - active_evaluations.count()}")
        self.stdout.write(f"Orphaned evaluations: {orphaned_count}")
        self.stdout.write(f"Invalid status evaluations: {invalid_status_count}")
        
        if not options['fix'] and (orphaned_count > 0 or invalid_status_count > 0):
            self.stdout.write(
                self.style.WARNING(
                    "\nRun with --fix to automatically resolve issues."
                )
            )
        
        self.stdout.write(
            self.style.SUCCESS('\nEvaluation sync check completed!')
        )
