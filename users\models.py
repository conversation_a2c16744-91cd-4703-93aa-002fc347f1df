"""
User models for Employee Rating System.
Implements the hierarchical user roles and department relationships as per PRD.
"""

from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models import BaseModel


class CustomUser(AbstractUser):
    """
    Custom user model extending Django's AbstractUser.
    Implements the role-based access control system from PRD.
    """
    
    USER_ROLES = [
        ('SUPER_ADMIN', _('Super Admin (مدير النظام الرئيسي)')),
        ('MANAGER', _('Manager/Department Head (مدير القسم)')),
        ('SUPERVISOR', _('Supervisor/Team Lead (مشرف الفريق)')),
        ('QUALITY_TEAM', _('Quality Team (فريق الجودة)')),
        ('EMPLOYEE', _('Employee (الموظف)')),
    ]
    
    # Employee identification
    employee_id = models.CharField(
        _('Employee ID'),
        max_length=50,
        unique=True,
        help_text=_('Unique employee identifier')
    )
    
    # Bilingual names
    arabic_name = models.CharField(
        _('Arabic Name (الاسم بالعربية)'),
        max_length=255,
        help_text=_('Full name in Arabic')
    )
    
    english_name = models.CharField(
        _('English Name'),
        max_length=255,
        help_text=_('Full name in English')
    )
    
    # Role-based access control
    role = models.CharField(
        _('User Role'),
        max_length=20,
        choices=USER_ROLES,
        default='EMPLOYEE',
        help_text=_('User role determines access permissions')
    )
    
    # Department relationships (many-to-many) - will be added in migration
    # primary_department = models.ForeignKey(
    #     'departments.Department',
    #     on_delete=models.SET_NULL,
    #     null=True,
    #     blank=True,
    #     related_name='primary_employees',
    #     verbose_name=_('Primary Department'),
    #     help_text=_('Main department assignment')
    # )

    # departments = models.ManyToManyField(
    #     'departments.Department',
    #     through='UserDepartment',
    #     related_name='employees',
    #     verbose_name=_('Departments'),
    #     help_text=_('All department assignments')
    # )
    
    # Additional fields
    phone_number = models.CharField(
        _('Phone Number'),
        max_length=20,
        blank=True,
        help_text=_('Contact phone number')
    )
    
    hire_date = models.DateField(
        _('Hire Date'),
        null=True,
        blank=True,
        help_text=_('Date of employment')
    )
    
    is_active = models.BooleanField(
        _('Active'),
        default=True,
        help_text=_('Designates whether this user should be treated as active.')
    )

    # Language preference
    language_preference = models.CharField(
        _('Language Preference'),
        max_length=5,
        choices=[('en', 'English'), ('ar', 'العربية')],
        default='en',
        help_text=_('User preferred language for interface')
    )

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)
    
    class Meta:
        verbose_name = _('User')
        verbose_name_plural = _('Users')
        ordering = ['english_name']
    
    def __str__(self):
        return f"{self.english_name} ({self.employee_id})"
    
    def get_display_name(self, language='en'):
        """Get display name based on language preference."""
        if language == 'ar':
            return self.arabic_name
        return self.english_name
    
    def get_managed_departments(self):
        """Get all departments managed by this user (for Managers)."""
        if self.role == 'MANAGER':
            return self.managed_departments.all()
        return []
    
    def get_accessible_departments(self):
        """Get all departments accessible to this user based on role."""
        # Will be implemented after department relationships are added
        return []

    def can_evaluate_user(self, target_user):
        """Check if this user can evaluate the target user."""
        # Will be implemented after department relationships are added
        return False


# class UserDepartment(BaseModel):
#     """
#     Through model for User-Department many-to-many relationship.
#     Allows tracking of role within department and assignment history.
#     """
#
#     DEPARTMENT_ROLES = [
#         ('MANAGER', _('Manager')),
#         ('SUPERVISOR', _('Supervisor')),
#         ('EMPLOYEE', _('Employee')),
#     ]
#
#     user = models.ForeignKey(
#         CustomUser,
#         on_delete=models.CASCADE,
#         verbose_name=_('User')
#     )
#
#     department = models.ForeignKey(
#         'departments.Department',
#         on_delete=models.CASCADE,
#         verbose_name=_('Department')
#     )
#
#     is_primary = models.BooleanField(
#         _('Is Primary Department'),
#         default=False,
#         help_text=_('Indicates if this is the user\'s primary department')
#     )
#
#     role_in_department = models.CharField(
#         _('Role in Department'),
#         max_length=20,
#         choices=DEPARTMENT_ROLES,
#         default='EMPLOYEE',
#         help_text=_('User\'s role within this specific department')
#     )
#
#     assigned_date = models.DateTimeField(
#         _('Assignment Date'),
#         auto_now_add=True,
#         help_text=_('Date when user was assigned to this department')
#     )
#
#     class Meta:
#         verbose_name = _('User Department Assignment')
#         verbose_name_plural = _('User Department Assignments')
#         unique_together = ['user', 'department']
#
#     def __str__(self):
#         return f"{self.user.english_name} - {self.department.name_en}"
