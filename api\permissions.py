"""
Custom permissions for Employee Rating System API.
Implements hierarchical access control as per PRD specifications.
"""

from rest_framework import permissions


class HierarchicalAccessPermission(permissions.BasePermission):
    """
    Custom permission class that implements hierarchical access control.
    Users can only access data within their department hierarchy.
    """
    
    def has_permission(self, request, view):
        """Check if user has permission to access the view."""
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        """Check if user has permission to access specific object."""
        user = request.user
        
        # Super Admin has access to everything
        if user.role == 'SUPER_ADMIN':
            return True
        
        # Quality Team has read-only access to everything
        if user.role == 'QUALITY_TEAM':
            return request.method in permissions.SAFE_METHODS
        
        # Handle different object types
        if hasattr(obj, 'can_user_access'):
            # Objects with custom access control method (e.g., Department)
            return obj.can_user_access(user)
        
        elif hasattr(obj, 'primary_department'):
            # User objects - check department access
            if user.role == 'MANAGER':
                accessible_depts = user.get_accessible_departments()
                return obj.primary_department in accessible_depts
            elif user.role == 'SUPERVISOR':
                return obj.primary_department == user.primary_department
            elif user.role == 'EMPLOYEE':
                return obj == user
        
        elif hasattr(obj, 'employee'):
            # Evaluation objects - check if user can evaluate the employee
            return user.can_evaluate_user(obj.employee) or obj.employee == user
        
        elif hasattr(obj, 'department'):
            # Department-related objects
            if user.role == 'MANAGER':
                accessible_depts = user.get_accessible_departments()
                return obj.department in accessible_depts
            elif user.role == 'SUPERVISOR':
                return obj.department == user.primary_department
        
        # Default deny
        return False


class SuperAdminOnlyPermission(permissions.BasePermission):
    """
    Permission class that only allows Super Admin users.
    Used for system configuration endpoints.
    """
    
    def has_permission(self, request, view):
        """Check if user is Super Admin."""
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'SUPER_ADMIN'
        )


class ManagerOrSuperAdminPermission(permissions.BasePermission):
    """
    Permission class that allows Manager and Super Admin users.
    Used for management-level operations.
    """
    
    def has_permission(self, request, view):
        """Check if user is Manager or Super Admin."""
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role in ['MANAGER', 'SUPER_ADMIN']
        )


class EvaluatorPermission(permissions.BasePermission):
    """
    Permission class for evaluation-related operations.
    Users can only evaluate employees they have access to.
    """
    
    def has_permission(self, request, view):
        """Check if user can perform evaluations."""
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role in ['SUPER_ADMIN', 'MANAGER', 'SUPERVISOR']
        )
    
    def has_object_permission(self, request, view, obj):
        """Check if user can evaluate specific employee."""
        user = request.user
        
        if user.role == 'SUPER_ADMIN':
            return True
        
        # For evaluation objects, check if user can evaluate the employee
        if hasattr(obj, 'employee'):
            return user.can_evaluate_user(obj.employee)
        
        # For user objects, check if current user can evaluate this user
        elif hasattr(obj, 'primary_department'):
            return user.can_evaluate_user(obj)
        
        return False


class ReadOnlyOrOwnerPermission(permissions.BasePermission):
    """
    Permission class that allows read-only access to all authenticated users,
    but write access only to the owner or authorized users.
    """
    
    def has_permission(self, request, view):
        """Check if user is authenticated."""
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        """Check object-level permissions."""
        user = request.user
        
        # Read permissions for all authenticated users
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Write permissions only for authorized users
        if user.role == 'SUPER_ADMIN':
            return True
        
        # Check if user owns the object
        if hasattr(obj, 'user') and obj.user == user:
            return True
        
        if hasattr(obj, 'employee') and obj.employee == user:
            return True
        
        # Check if user can manage the object based on hierarchy
        if hasattr(obj, 'primary_department'):
            return user.can_evaluate_user(obj)
        
        return False
