{% extends 'base.html' %}

{% block title %}
    {% if LANGUAGE_CODE == 'ar' %}
        {{ department.name_ar|default:department.name_en }} - الأقسام - نظام تقييم الموظفين
    {% else %}
        {{ department.name_en }} - Departments - Employee Rating System
    {% endif %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h2 text-gray-900 mb-1">
            {% if LANGUAGE_CODE == 'ar' %}
                {{ department.name_ar|default:department.name_en }}
            {% else %}
                {{ department.name_en }}
            {% endif %}
        </h1>
        <p class="text-muted">
            {% if LANGUAGE_CODE == 'ar' %}
                تفاصيل القسم ومعلومات الموظفين
            {% else %}
                Department details and employee information
            {% endif %}
        </p>
    </div>
    <div class="d-flex gap-2">
        {% if user.role in 'SUPER_ADMIN,MANAGER' %}
        <a href="{% url 'departments:edit' department.pk %}" class="btn btn-outline-primary">
            <i class="fas fa-edit me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}Edit{% endif %}
        </a>
        {% if user.role == 'SUPER_ADMIN' %}
        <button type="button" class="btn btn-outline-danger" onclick="confirmDelete()">
            <i class="fas fa-trash me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}حذف{% else %}Delete{% endif %}
        </button>
        {% endif %}
        {% endif %}
        <a href="{% url 'departments:list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}العودة{% else %}Back{% endif %}
        </a>
    </div>
</div>

<!-- Department Overview Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon me-3" style="background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);">
                    <i class="fas fa-users text-white"></i>
                </div>
                <div>
                    <div class="h4 mb-0">{{ employee_count|default:0 }}</div>
                    <small class="text-muted">
                        {% if LANGUAGE_CODE == 'ar' %}إجمالي الموظفين{% else %}Total Employees{% endif %}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon me-3" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                    <i class="fas fa-user-tie text-white"></i>
                </div>
                <div>
                    <div class="h4 mb-0">{{ manager_count|default:0 }}</div>
                    <small class="text-muted">
                        {% if LANGUAGE_CODE == 'ar' %}المديرون{% else %}Managers{% endif %}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon me-3" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                    <i class="fas fa-clipboard-list text-white"></i>
                </div>
                <div>
                    <div class="h4 mb-0">{{ evaluation_count|default:0 }}</div>
                    <small class="text-muted">
                        {% if LANGUAGE_CODE == 'ar' %}التقييمات{% else %}Evaluations{% endif %}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon me-3" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                    <i class="fas fa-{{ department.is_active|yesno:'check-circle,times-circle' }} text-white"></i>
                </div>
                <div>
                    <div class="h6 mb-0">
                        {% if department.is_active %}
                            {% if LANGUAGE_CODE == 'ar' %}نشط{% else %}Active{% endif %}
                        {% else %}
                            {% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}Inactive{% endif %}
                        {% endif %}
                    </div>
                    <small class="text-muted">
                        {% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}Status{% endif %}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Department Information -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}معلومات القسم{% else %}Department Information{% endif %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-semibold text-muted">
                            {% if LANGUAGE_CODE == 'ar' %}الاسم بالإنجليزية{% else %}English Name{% endif %}
                        </label>
                        <div class="form-control-plaintext">{{ department.name_en }}</div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-semibold text-muted">
                            {% if LANGUAGE_CODE == 'ar' %}الاسم بالعربية{% else %}Arabic Name{% endif %}
                        </label>
                        <div class="form-control-plaintext">{{ department.name_ar|default:"-" }}</div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-semibold text-muted">
                            {% if LANGUAGE_CODE == 'ar' %}كود القسم{% else %}Department Code{% endif %}
                        </label>
                        <div class="form-control-plaintext">
                            <span class="badge bg-primary">{{ department.code }}</span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-semibold text-muted">
                            {% if LANGUAGE_CODE == 'ar' %}تاريخ الإنشاء{% else %}Created Date{% endif %}
                        </label>
                        <div class="form-control-plaintext">{{ department.created_at|date:"Y-m-d" }}</div>
                    </div>
                    {% if department.parent %}
                    <div class="col-12 mb-3">
                        <label class="form-label fw-semibold text-muted">
                            {% if LANGUAGE_CODE == 'ar' %}القسم الرئيسي{% else %}Parent Department{% endif %}
                        </label>
                        <div class="form-control-plaintext">
                            <a href="{% url 'departments:detail' department.parent.pk %}" class="text-decoration-none">
                                <i class="fas fa-building me-2"></i>
                                {% if LANGUAGE_CODE == 'ar' %}
                                    {{ department.parent.name_ar|default:department.parent.name_en }}
                                {% else %}
                                    {{ department.parent.name_en }}
                                {% endif %}
                            </a>
                        </div>
                    </div>
                    {% endif %}
                    <div class="col-12">
                        <label class="form-label fw-semibold text-muted">
                            {% if LANGUAGE_CODE == 'ar' %}الوصف{% else %}Description{% endif %}
                        </label>
                        <div class="form-control-plaintext">
                            {% if LANGUAGE_CODE == 'ar' %}
                                {{ department.description_ar|default:department.description_en|default:"-" }}
                            {% else %}
                                {{ department.description_en|default:"-" }}
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employees List -->
        {% if employees %}
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-users me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}الموظفين ({{ employees.count }}){% else %}Employees ({{ employees.count }}){% endif %}
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>
                                    {% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}Name{% endif %}
                                </th>
                                <th>
                                    {% if LANGUAGE_CODE == 'ar' %}رقم الموظف{% else %}Employee ID{% endif %}
                                </th>
                                <th>
                                    {% if LANGUAGE_CODE == 'ar' %}المنصب{% else %}Role{% endif %}
                                </th>
                                <th>
                                    {% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}Status{% endif %}
                                </th>
                                <th>
                                    {% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}Actions{% endif %}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm me-3">
                                            <div class="avatar-title bg-primary rounded-circle">
                                                {{ employee.english_name|first|upper }}
                                            </div>
                                        </div>
                                        <div>
                                            <div class="fw-semibold">
                                                {% if LANGUAGE_CODE == 'ar' %}
                                                    {{ employee.arabic_name|default:employee.english_name }}
                                                {% else %}
                                                    {{ employee.english_name }}
                                                {% endif %}
                                            </div>
                                            <small class="text-muted">{{ employee.email }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">{{ employee.employee_id }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ employee.get_role_display }}</span>
                                </td>
                                <td>
                                    {% if employee.is_active %}
                                        <span class="badge bg-success">
                                            {% if LANGUAGE_CODE == 'ar' %}نشط{% else %}Active{% endif %}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">
                                            {% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}Inactive{% endif %}
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'users:profile' employee.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye me-1"></i>
                                        {% if LANGUAGE_CODE == 'ar' %}عرض{% else %}View{% endif %}
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% else %}
        <div class="card shadow">
            <div class="card-body text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">
                    {% if LANGUAGE_CODE == 'ar' %}لا يوجد موظفين في هذا القسم{% else %}No employees in this department{% endif %}
                </h5>
                <p class="text-muted">
                    {% if LANGUAGE_CODE == 'ar' %}
                        لم يتم تعيين أي موظفين لهذا القسم بعد
                    {% else %}
                        No employees have been assigned to this department yet
                    {% endif %}
                </p>
            </div>
        </div>
        {% endif %}
    </div>

    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}إجراءات سريعة{% else %}Quick Actions{% endif %}
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if user.role in 'SUPER_ADMIN,MANAGER' %}
                    <a href="{% url 'departments:edit' department.pk %}" class="btn btn-outline-primary">
                        <i class="fas fa-edit me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}تعديل القسم{% else %}Edit Department{% endif %}
                    </a>
                    <a href="{% url 'evaluations:create' %}" class="btn btn-outline-success">
                        <i class="fas fa-plus me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}إنشاء تقييم{% else %}Create Evaluation{% endif %}
                    </a>
                    {% endif %}
                    <a href="{% url 'departments:hierarchy' %}" class="btn btn-outline-info">
                        <i class="fas fa-sitemap me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}الهيكل التنظيمي{% else %}View Hierarchy{% endif %}
                    </a>
                    <a href="{% url 'departments:list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}جميع الأقسام{% else %}All Departments{% endif %}
                    </a>
                </div>
            </div>
        </div>

        <!-- Department Hierarchy -->
        {% if department.parent or department.children.exists %}
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-sitemap me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}الهيكل التنظيمي{% else %}Department Hierarchy{% endif %}
                </h6>
            </div>
            <div class="card-body">
                {% if department.parent %}
                <div class="mb-3">
                    <label class="form-label fw-semibold text-muted">
                        {% if LANGUAGE_CODE == 'ar' %}القسم الرئيسي{% else %}Parent Department{% endif %}
                    </label>
                    <div>
                        <a href="{% url 'departments:detail' department.parent.pk %}" class="text-decoration-none">
                            <i class="fas fa-building me-2 text-primary"></i>
                            {% if LANGUAGE_CODE == 'ar' %}
                                {{ department.parent.name_ar|default:department.parent.name_en }}
                            {% else %}
                                {{ department.parent.name_en }}
                            {% endif %}
                        </a>
                    </div>
                </div>
                {% endif %}

                {% if department.children.exists %}
                <div>
                    <label class="form-label fw-semibold text-muted">
                        {% if LANGUAGE_CODE == 'ar' %}الأقسام الفرعية{% else %}Sub-departments{% endif %}
                    </label>
                    <div class="list-group list-group-flush">
                        {% for child in department.children.all %}
                        <a href="{% url 'departments:detail' child.pk %}" class="list-group-item list-group-item-action border-0 px-0">
                            <i class="fas fa-building me-2 text-primary"></i>
                            {% if LANGUAGE_CODE == 'ar' %}
                                {{ child.name_ar|default:child.name_en }}
                            {% else %}
                                {{ child.name_en }}
                            {% endif %}
                            <small class="text-muted">({{ child.get_employee_count }}
                                {% if LANGUAGE_CODE == 'ar' %}موظف{% else %}employees{% endif %})
                            </small>
                        </a>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    {% if LANGUAGE_CODE == 'ar' %}تأكيد الحذف{% else %}Confirm Delete{% endif %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning"></i>
                </div>
                <p class="text-center">
                    {% if LANGUAGE_CODE == 'ar' %}
                        هل أنت متأكد من حذف القسم "{{ department.name_ar|default:department.name_en }}"؟
                    {% else %}
                        Are you sure you want to delete the department "{{ department.name_en }}"?
                    {% endif %}
                </p>
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                        هذا الإجراء لا يمكن التراجع عنه وسيؤثر على جميع الموظفين المرتبطين بهذا القسم.
                    {% else %}
                        This action cannot be undone and will affect all employees associated with this department.
                    {% endif %}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}Cancel{% endif %}
                </button>
                <form method="post" action="{% url 'departments:delete' department.pk %}" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}حذف نهائياً{% else %}Delete Permanently{% endif %}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.list-group-item-action:hover {
    background-color: rgba(0, 123, 255, 0.05);
}
</style>

<script>
function confirmDelete() {
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
{% endblock %}
