# Employee Rating System - Makefile
# Convenient commands for development and deployment

.PHONY: help install install-dev install-prod setup migrate test clean lint format run shell backup restore

# Default target
help:
	@echo "Employee Rating System - Available Commands:"
	@echo ""
	@echo "Setup Commands:"
	@echo "  install      - Install production dependencies"
	@echo "  install-dev  - Install development dependencies"
	@echo "  install-prod - Install production dependencies"
	@echo "  setup        - Complete project setup (dev environment)"
	@echo ""
	@echo "Database Commands:"
	@echo "  migrate      - Run database migrations"
	@echo "  makemigrations - Create new migrations"
	@echo "  reset-db     - Reset database (development only)"
	@echo "  backup       - Backup database"
	@echo "  restore      - Restore database from backup"
	@echo ""
	@echo "Development Commands:"
	@echo "  run          - Run development server"
	@echo "  shell        - Open Django shell"
	@echo "  test         - Run tests"
	@echo "  test-cov     - Run tests with coverage"
	@echo "  lint         - Run code linting"
	@echo "  format       - Format code"
	@echo "  clean        - Clean temporary files"
	@echo ""
	@echo "Production Commands:"
	@echo "  collectstatic - Collect static files"
	@echo "  check        - Run deployment checks"
	@echo "  gunicorn     - Run with Gunicorn"
	@echo ""

# Installation commands
install:
	pip install -r requirements.txt

install-dev:
	pip install -r requirements-dev.txt

install-prod:
	pip install -r requirements-prod.txt

# Setup command for new development environment
setup: install-dev
	@echo "Setting up development environment..."
	@if [ ! -f .env ]; then cp .env.example .env; echo "Created .env file from template"; fi
	python manage.py makemigrations
	python manage.py migrate
	@echo "Setup complete! Run 'make run' to start the development server."
	@echo "Don't forget to create a superuser: python manage.py createsuperuser"

# Database commands
migrate:
	python manage.py migrate

makemigrations:
	python manage.py makemigrations

reset-db:
	@echo "WARNING: This will delete all data in the database!"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ]
	rm -f db.sqlite3 db_development.sqlite3
	python manage.py makemigrations
	python manage.py migrate
	@echo "Database reset complete. Create a new superuser with: python manage.py createsuperuser"

backup:
	@echo "Creating database backup..."
	python manage.py dumpdata --natural-foreign --natural-primary > backup_$(shell date +%Y%m%d_%H%M%S).json
	@echo "Backup created successfully"

restore:
	@echo "Restoring database from backup..."
	@read -p "Enter backup filename: " filename && python manage.py loaddata $$filename
	@echo "Database restored successfully"

# Development commands
run:
	python manage.py runserver

shell:
	python manage.py shell

test:
	python manage.py test

test-cov:
	coverage run --source='.' manage.py test
	coverage report
	coverage html

lint:
	flake8 .
	isort --check-only .
	black --check .

format:
	isort .
	black .

clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type f -name "*.log" -delete
	rm -rf .coverage htmlcov/
	rm -rf .pytest_cache/
	rm -rf .tox/

# Production commands
collectstatic:
	python manage.py collectstatic --noinput

check:
	python manage.py check --deploy

gunicorn:
	gunicorn employee_rating.wsgi:application --bind 0.0.0.0:8000 --workers 4

# Translation commands
makemessages:
	python manage.py makemessages -l ar
	python manage.py makemessages -l en

compilemessages:
	python manage.py compilemessages

# Docker commands (if using Docker)
docker-build:
	docker build -t employee-rating .

docker-run:
	docker run -p 8000:8000 employee-rating

docker-compose-up:
	docker-compose up -d

docker-compose-down:
	docker-compose down

# Utility commands
logs:
	tail -f logs/employee_rating.log

create-superuser:
	python manage.py createsuperuser

load-sample-data:
	python manage.py loaddata fixtures/sample_departments.json
	python manage.py loaddata fixtures/sample_categories.json

# Environment-specific commands
dev: install-dev setup run

prod: install-prod collectstatic check

# Security commands
security-check:
	python manage.py check --deploy
	safety check
	bandit -r .

# Performance commands
profile:
	python manage.py runprofileserver

# API documentation
docs:
	@echo "API Documentation available at:"
	@echo "Swagger UI: http://localhost:8000/api/v1/docs/"
	@echo "ReDoc: http://localhost:8000/api/v1/redoc/"
	@echo "OpenAPI Schema: http://localhost:8000/api/v1/schema/"
