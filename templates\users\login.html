{% extends 'base.html' %}

{% block title %}
    {% if LANGUAGE_CODE == 'ar' %}
        تسجيل الدخول - نظام تقييم الموظفين
    {% else %}
        Login - Employee Rating System
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid" style="max-width: 450px;">
    <div class="text-center mb-4">
        <h1 class="h2 text-gray-900 mb-2">
            {% if LANGUAGE_CODE == 'ar' %}
                تسجيل الدخول
            {% else %}
                Welcome Back
            {% endif %}
        </h1>
        <p class="text-muted">
            {% if LANGUAGE_CODE == 'ar' %}
                أدخل بياناتك للوصول إلى حسابك
            {% else %}
                Enter your credentials to access your account
            {% endif %}
        </p>
    </div>

    <div class="card">
        <div class="card-body p-4">
            <form method="post">
                {% csrf_token %}

                <div class="mb-3">
                    <label for="username" class="form-label fw-semibold">
                        {% if LANGUAGE_CODE == 'ar' %}اسم المستخدم{% else %}Username{% endif %}
                    </label>
                    <input type="text" class="form-control" id="username" name="username"
                           placeholder="{% if LANGUAGE_CODE == 'ar' %}أدخل اسم المستخدم{% else %}Enter your username{% endif %}" required>
                </div>

                <div class="mb-4">
                    <label for="password" class="form-label fw-semibold">
                        {% if LANGUAGE_CODE == 'ar' %}كلمة المرور{% else %}Password{% endif %}
                    </label>
                    <input type="password" class="form-control" id="password" name="password"
                           placeholder="{% if LANGUAGE_CODE == 'ar' %}أدخل كلمة المرور{% else %}Enter your password{% endif %}" required>
                </div>

                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}تسجيل الدخول{% else %}Sign In{% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="text-center mt-4">
        <p class="text-muted">
            {% if LANGUAGE_CODE == 'ar' %}
                ليس لديك حساب؟
                <a href="{% url 'users:register' %}" class="text-decoration-none">إنشاء حساب جديد</a>
            {% else %}
                Don't have an account?
                <a href="{% url 'users:register' %}" class="text-decoration-none">Create New Account</a>
            {% endif %}
        </p>
    </div>
</div>

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{% endblock %}
