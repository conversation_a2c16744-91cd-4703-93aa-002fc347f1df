[tool:pytest]
DJANGO_SETTINGS_MODULE = employee_rating.settings.development
python_files = tests.py test_*.py *_tests.py
python_classes = Test*
python_functions = test_*
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --reuse-db
    --nomigrations
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    api: marks tests as API tests
    models: marks tests as model tests
    views: marks tests as view tests
    permissions: marks tests as permission tests
testpaths = 
    tests/
    core/tests/
    users/tests/
    departments/tests/
    evaluations/tests/
    api/tests/
