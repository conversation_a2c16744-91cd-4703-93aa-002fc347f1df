"""
Department models for Employee Rating System.
Implements hierarchical department structure with unlimited nesting as per PRD.
"""

from django.db import models
from django.utils.translation import gettext_lazy as _
from mptt.models import MPTTModel, TreeForeign<PERSON>ey
from core.models import BaseModel


class Department(MPTTModel, BaseModel):
    """
    Department model with hierarchical structure using MPTT.
    Supports unlimited nesting levels as specified in PRD.
    """
    
    # Bilingual names
    name_en = models.CharField(
        _('English Name'),
        max_length=255,
        help_text=_('Department name in English')
    )
    
    name_ar = models.Char<PERSON><PERSON>(
        _('Arabic Name (الاسم بالعربية)'),
        max_length=255,
        help_text=_('Department name in Arabic')
    )
    
    # Unique department code
    code = models.Char<PERSON>ield(
        _('Department Code'),
        max_length=50,
        unique=True,
        help_text=_('Unique department identifier code')
    )
    
    # Hierarchical structure
    parent = TreeForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name=_('Parent Department'),
        help_text=_('Parent department in hierarchy')
    )
    
    # Department managers (many-to-many)
    managers = models.ManyToManyField(
        'users.CustomUser',
        related_name='managed_departments',
        blank=True,
        verbose_name=_('Department Managers'),
        help_text=_('Users who manage this department')
    )
    
    # Additional information
    description_en = models.TextField(
        _('English Description'),
        blank=True,
        help_text=_('Department description in English')
    )
    
    description_ar = models.TextField(
        _('Arabic Description (الوصف بالعربية)'),
        blank=True,
        help_text=_('Department description in Arabic')
    )
    
    # Status
    is_active = models.BooleanField(
        _('Active'),
        default=True,
        help_text=_('Whether this department is currently active')
    )
    
    class MPTTMeta:
        order_insertion_by = ['name_en']
    
    class Meta:
        verbose_name = _('Department')
        verbose_name_plural = _('Departments')
        ordering = ['tree_id', 'lft']
    
    def __str__(self):
        return f"{self.name_en} ({self.code})"
    
    def get_display_name(self, language='en'):
        """Get display name based on language preference."""
        if language == 'ar':
            return self.name_ar
        return self.name_en
    
    def get_full_path(self, language='en', separator=' > '):
        """Get full hierarchical path of the department."""
        ancestors = self.get_ancestors(include_self=True)
        if language == 'ar':
            names = [dept.name_ar for dept in ancestors]
        else:
            names = [dept.name_en for dept in ancestors]
        return separator.join(names)
    
    def get_employee_count(self):
        """Get total number of employees in this department and all sub-departments."""
        # For now, return 0 since department relationships are not fully implemented
        # This will be updated when department-user relationships are added
        return 0
    
    def get_direct_employees(self):
        """Get employees directly assigned to this department."""
        # For now, return empty queryset since department relationships are not fully implemented
        from users.models import CustomUser
        return CustomUser.objects.none()
    
    def get_all_employees(self):
        """Get all employees in this department and sub-departments."""
        # For now, return empty queryset since department relationships are not fully implemented
        from users.models import CustomUser
        return CustomUser.objects.none()
    
    def can_user_access(self, user):
        """Check if a user can access this department based on their role."""
        if user.role == 'SUPER_ADMIN':
            return True
        elif user.role == 'MANAGER':
            # Check if user manages this department or any of its ancestors
            managed_depts = user.managed_departments.all()
            ancestors = self.get_ancestors(include_self=True)
            return any(dept in managed_depts for dept in ancestors)
        elif user.role == 'SUPERVISOR':
            return self == user.primary_department
        elif user.role == 'QUALITY_TEAM':
            return True  # Read-only access to all departments
        return False
    
    def get_managers_hierarchy(self):
        """Get all managers in the department hierarchy (self and ancestors)."""
        ancestors = self.get_ancestors(include_self=True)
        managers = []
        for dept in ancestors:
            managers.extend(dept.managers.all())
        return list(set(managers))  # Remove duplicates


class DepartmentHierarchyView(models.Model):
    """
    Model to store pre-computed department hierarchy views for performance.
    This can be used for complex reporting and analytics.
    """
    
    department = models.OneToOneField(
        Department,
        on_delete=models.CASCADE,
        related_name='hierarchy_view',
        verbose_name=_('Department')
    )
    
    # Pre-computed hierarchy information
    level = models.PositiveIntegerField(
        _('Hierarchy Level'),
        help_text=_('Level in department hierarchy (0 = root)')
    )
    
    path = models.TextField(
        _('Full Path'),
        help_text=_('Full hierarchical path')
    )
    
    path_ar = models.TextField(
        _('Full Path (Arabic)'),
        help_text=_('Full hierarchical path in Arabic')
    )
    
    total_employees = models.PositiveIntegerField(
        _('Total Employees'),
        default=0,
        help_text=_('Total employees including sub-departments')
    )
    
    direct_employees = models.PositiveIntegerField(
        _('Direct Employees'),
        default=0,
        help_text=_('Employees directly assigned to this department')
    )
    
    last_updated = models.DateTimeField(
        _('Last Updated'),
        auto_now=True
    )
    
    class Meta:
        verbose_name = _('Department Hierarchy View')
        verbose_name_plural = _('Department Hierarchy Views')
    
    def __str__(self):
        return f"Hierarchy View: {self.department.name_en}"
    
    @classmethod
    def refresh_all(cls):
        """Refresh all hierarchy views."""
        for dept in Department.objects.all():
            view, created = cls.objects.get_or_create(department=dept)
            view.level = dept.level
            view.path = dept.get_full_path('en')
            view.path_ar = dept.get_full_path('ar')
            view.total_employees = dept.get_employee_count()
            view.direct_employees = dept.get_direct_employees().count()
            view.save()
