{% extends 'base.html' %}

{% block title %}Organization Chart - Employee Rating System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Organization Chart</h1>
    <a href="{% url 'departments:list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-list me-2"></i>List View
    </a>
</div>

<div class="card">
    <div class="card-body">
        {% if root_departments %}
            {% for department in root_departments %}
            <div class="department-tree">
                <div class="department-node">
                    <div class="card mb-2">
                        <div class="card-body py-2">
                            <h6 class="mb-1">{{ department.name_en }}</h6>
                            <small class="text-muted">{{ department.code }}</small>
                        </div>
                    </div>
                </div>
                
                {% if department.get_children %}
                <div class="department-children ms-4">
                    {% for child in department.get_children %}
                    <div class="card mb-2">
                        <div class="card-body py-2">
                            <h6 class="mb-1">{{ child.name_en }}</h6>
                            <small class="text-muted">{{ child.code }}</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        {% else %}
        <div class="text-center">
            <i class="fas fa-sitemap fa-3x text-muted mb-3"></i>
            <p class="text-muted">No departments available to display.</p>
        </div>
        {% endif %}
    </div>
</div>

<style>
.department-tree {
    margin-bottom: 2rem;
}
.department-children {
    border-left: 2px solid #dee2e6;
    padding-left: 1rem;
}
</style>
{% endblock %}
