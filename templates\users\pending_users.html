{% extends 'base.html' %}

{% block title %}
    {% if LANGUAGE_CODE == 'ar' %}
        طلبات التسجيل المعلقة - نظام تقييم الموظفين
    {% else %}
        Pending User Registrations - Employee Rating System
    {% endif %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h2 text-gray-900 mb-1">
            {% if LANGUAGE_CODE == 'ar' %}
                طلبات التسجيل المعلقة
            {% else %}
                Pending User Registrations
            {% endif %}
        </h1>
        <p class="text-muted">
            {% if LANGUAGE_CODE == 'ar' %}
                مراجعة وإدارة طلبات إنشاء الحسابات الجديدة
            {% else %}
                Review and manage new account registration requests
            {% endif %}
        </p>
    </div>
    
    <div class="stat-card" style="min-width: 200px;">
        <div class="d-flex align-items-center">
            <div class="stat-icon me-3">
                <i class="fas fa-clock text-white"></i>
            </div>
            <div>
                <div class="h4 mb-0">{{ total_pending }}</div>
                <small class="text-muted">
                    {% if LANGUAGE_CODE == 'ar' %}طلبات معلقة{% else %}Pending Requests{% endif %}
                </small>
            </div>
        </div>
    </div>
</div>

{% if pending_users %}
<div class="row">
    {% for user in pending_users %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100">
            <div class="card-body">
                <div class="d-flex align-items-start mb-3">
                    <div class="profile-avatar me-3">
                        {{ user.english_name.0|default:user.username.0|upper }}
                    </div>
                    <div class="flex-grow-1">
                        <h5 class="card-title mb-1">{{ user.english_name|default:user.username }}</h5>
                        {% if user.arabic_name %}
                        <p class="text-muted small mb-1">{{ user.arabic_name }}</p>
                        {% endif %}
                        <span class="badge bg-warning text-dark">
                            {% if LANGUAGE_CODE == 'ar' %}في انتظار الموافقة{% else %}Pending Approval{% endif %}
                        </span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="row g-2 small">
                        <div class="col-6">
                            <strong>
                                {% if LANGUAGE_CODE == 'ar' %}اسم المستخدم:{% else %}Username:{% endif %}
                            </strong>
                        </div>
                        <div class="col-6">{{ user.username }}</div>
                        
                        <div class="col-6">
                            <strong>
                                {% if LANGUAGE_CODE == 'ar' %}رقم الموظف:{% else %}Employee ID:{% endif %}
                            </strong>
                        </div>
                        <div class="col-6">{{ user.employee_id }}</div>
                        
                        <div class="col-6">
                            <strong>
                                {% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني:{% else %}Email:{% endif %}
                            </strong>
                        </div>
                        <div class="col-6">
                            <small>{{ user.email }}</small>
                        </div>
                        
                        {% if user.phone_number %}
                        <div class="col-6">
                            <strong>
                                {% if LANGUAGE_CODE == 'ar' %}الهاتف:{% else %}Phone:{% endif %}
                            </strong>
                        </div>
                        <div class="col-6">{{ user.phone_number }}</div>
                        {% endif %}
                        
                        <div class="col-6">
                            <strong>
                                {% if LANGUAGE_CODE == 'ar' %}تاريخ التسجيل:{% else %}Registered:{% endif %}
                            </strong>
                        </div>
                        <div class="col-6">
                            <small>{{ user.date_joined|date:"M d, Y" }}</small>
                        </div>
                    </div>
                </div>
                
                <!-- Approval Form -->
                <form class="approval-form" data-user-id="{{ user.id }}">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label class="form-label small fw-semibold">
                            {% if LANGUAGE_CODE == 'ar' %}الدور{% else %}Role{% endif %}
                        </label>
                        <select name="role" class="form-select form-select-sm">
                            <option value="EMPLOYEE">
                                {% if LANGUAGE_CODE == 'ar' %}موظف{% else %}Employee{% endif %}
                            </option>
                            <option value="SUPERVISOR">
                                {% if LANGUAGE_CODE == 'ar' %}مشرف{% else %}Supervisor{% endif %}
                            </option>
                            <option value="MANAGER">
                                {% if LANGUAGE_CODE == 'ar' %}مدير{% else %}Manager{% endif %}
                            </option>
                            <option value="QUALITY_TEAM">
                                {% if LANGUAGE_CODE == 'ar' %}فريق الجودة{% else %}Quality Team{% endif %}
                            </option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label small fw-semibold">
                            {% if LANGUAGE_CODE == 'ar' %}الأقسام{% else %}Departments{% endif %}
                        </label>
                        <select name="departments" class="form-select form-select-sm" multiple size="3">
                            {% for department in departments %}
                            <option value="{{ department.id }}">{{ department.name_en }}</option>
                            {% endfor %}
                        </select>
                        <small class="text-muted">
                            {% if LANGUAGE_CODE == 'ar' %}اضغط Ctrl للاختيار المتعدد{% else %}Hold Ctrl for multiple selection{% endif %}
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label small fw-semibold">
                            {% if LANGUAGE_CODE == 'ar' %}القسم الأساسي{% else %}Primary Department{% endif %}
                        </label>
                        <select name="primary_department" class="form-select form-select-sm">
                            <option value="">
                                {% if LANGUAGE_CODE == 'ar' %}اختر القسم الأساسي{% else %}Select Primary Department{% endif %}
                            </option>
                            {% for department in departments %}
                            <option value="{{ department.id }}">{{ department.name_en }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-success btn-sm approve-btn" data-user-id="{{ user.id }}">
                            <i class="fas fa-check me-1"></i>
                            {% if LANGUAGE_CODE == 'ar' %}موافقة وتفعيل{% else %}Approve & Activate{% endif %}
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm reject-btn" data-user-id="{{ user.id }}">
                            <i class="fas fa-times me-1"></i>
                            {% if LANGUAGE_CODE == 'ar' %}رفض وحذف{% else %}Reject & Delete{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if pending_users.has_other_pages %}
<nav aria-label="Page navigation" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if pending_users.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ pending_users.previous_page_number }}">
                    {% if LANGUAGE_CODE == 'ar' %}السابق{% else %}Previous{% endif %}
                </a>
            </li>
        {% endif %}
        
        {% for num in pending_users.paginator.page_range %}
            {% if pending_users.number == num %}
                <li class="page-item active">
                    <span class="page-link">{{ num }}</span>
                </li>
            {% else %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                </li>
            {% endif %}
        {% endfor %}
        
        {% if pending_users.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ pending_users.next_page_number }}">
                    {% if LANGUAGE_CODE == 'ar' %}التالي{% else %}Next{% endif %}
                </a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<div class="text-center py-5">
    <div class="stat-card d-inline-block">
        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
        <h4>
            {% if LANGUAGE_CODE == 'ar' %}
                لا توجد طلبات معلقة
            {% else %}
                No Pending Requests
            {% endif %}
        </h4>
        <p class="text-muted">
            {% if LANGUAGE_CODE == 'ar' %}
                جميع طلبات التسجيل تم التعامل معها
            {% else %}
                All registration requests have been processed
            {% endif %}
        </p>
    </div>
</div>
{% endif %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle approve button clicks
    document.querySelectorAll('.approve-btn').forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.dataset.userId;
            const form = this.closest('.approval-form');
            const formData = new FormData(form);
            
            // Show loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
            this.disabled = true;
            
            fetch(`{% url 'users:approve_user' 0 %}`.replace('0', userId), {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Remove the card with animation
                    const card = this.closest('.col-lg-6');
                    card.style.transition = 'all 0.3s ease';
                    card.style.opacity = '0';
                    card.style.transform = 'scale(0.9)';
                    setTimeout(() => card.remove(), 300);
                    
                    // Show success message
                    showAlert('success', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                this.innerHTML = '<i class="fas fa-check me-1"></i>Approve & Activate';
                this.disabled = false;
            });
        });
    });
    
    // Handle reject button clicks
    document.querySelectorAll('.reject-btn').forEach(button => {
        button.addEventListener('click', function() {
            if (!confirm('Are you sure you want to reject and delete this user registration?')) {
                return;
            }
            
            const userId = this.dataset.userId;
            
            // Show loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
            this.disabled = true;
            
            fetch(`{% url 'users:reject_user' 0 %}`.replace('0', userId), {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Remove the card with animation
                    const card = this.closest('.col-lg-6');
                    card.style.transition = 'all 0.3s ease';
                    card.style.opacity = '0';
                    card.style.transform = 'scale(0.9)';
                    setTimeout(() => card.remove(), 300);
                    
                    // Show success message
                    showAlert('warning', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                this.innerHTML = '<i class="fas fa-times me-1"></i>Reject & Delete';
                this.disabled = false;
            });
        });
    });
});

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
