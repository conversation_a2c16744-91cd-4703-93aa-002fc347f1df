"""
API views for Employee Rating System.
Implements role-based access control as per PRD specifications.
"""

from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.utils import timezone

from users.models import CustomUser
from departments.models import Department
from evaluations.models import (
    EvaluationCategory, EvaluationQuestion, 
    Evaluation, EvaluationResponse
)
from .serializers import (
    UserSerializer, DepartmentSerializer, EvaluationCategorySerializer,
    EvaluationQuestionSerializer, EvaluationSerializer, EvaluationResponseSerializer
)
from .permissions import HierarchicalAccessPermission


class DepartmentViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Department model with hierarchical access control.
    """
    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['is_active', 'parent']
    search_fields = ['name_en', 'name_ar', 'code']
    ordering_fields = ['name_en', 'created_at']
    ordering = ['tree_id', 'lft']
    
    def get_queryset(self):
        """Filter departments based on user's role and access."""
        user = self.request.user
        
        if user.role == 'SUPER_ADMIN':
            return Department.objects.all()
        elif user.role == 'MANAGER':
            accessible_depts = user.get_accessible_departments()
            return Department.objects.filter(id__in=[dept.id for dept in accessible_depts])
        elif user.role == 'SUPERVISOR':
            # For now, return empty queryset since department relationships are not fully implemented
            return Department.objects.none()
        elif user.role == 'QUALITY_TEAM':
            return Department.objects.all()  # Read-only access
        else:
            return Department.objects.none()
    
    @action(detail=True, methods=['get'])
    def employees(self, request, pk=None):
        """Get employees in a department."""
        department = self.get_object()
        
        # Check if user can access this department
        if not department.can_user_access(request.user):
            return Response(
                {'error': 'Permission denied'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Get employees based on user role
        if request.user.role in ['SUPER_ADMIN', 'MANAGER']:
            employees = department.get_all_employees()
        else:
            employees = department.get_direct_employees()
        
        serializer = UserSerializer(employees, many=True, context={'request': request})
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def hierarchy(self, request):
        """Get department hierarchy tree."""
        queryset = self.get_queryset()
        root_departments = queryset.filter(parent=None)
        serializer = self.get_serializer(root_departments, many=True)
        return Response(serializer.data)


class UserViewSet(viewsets.ModelViewSet):
    """
    ViewSet for CustomUser model with role-based access control.
    """
    queryset = CustomUser.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['role', 'is_active']
    search_fields = ['username', 'english_name', 'arabic_name', 'employee_id', 'email']
    ordering_fields = ['english_name', 'date_joined']
    ordering = ['english_name']
    
    def get_queryset(self):
        """Filter users based on role and department access."""
        user = self.request.user
        
        if user.role == 'SUPER_ADMIN':
            return CustomUser.objects.all()
        elif user.role == 'MANAGER':
            accessible_depts = user.get_accessible_departments()
            return CustomUser.objects.filter(
                primary_department__in=accessible_depts
            )
        elif user.role == 'SUPERVISOR':
            if user.primary_department:
                return user.primary_department.get_direct_employees()
            return CustomUser.objects.none()
        elif user.role == 'QUALITY_TEAM':
            return CustomUser.objects.all()  # Read-only access
        else:
            return CustomUser.objects.filter(id=user.id)  # Only self
    
    @action(detail=True, methods=['get'])
    def evaluations(self, request, pk=None):
        """Get evaluations for a user."""
        user_obj = self.get_object()
        
        # Check if current user can view this user's evaluations
        if not request.user.can_evaluate_user(user_obj) and user_obj != request.user:
            return Response(
                {'error': 'Permission denied'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        evaluations = Evaluation.objects.filter(employee=user_obj)
        serializer = EvaluationSerializer(evaluations, many=True, context={'request': request})
        return Response(serializer.data)


class EvaluationCategoryViewSet(viewsets.ModelViewSet):
    """
    ViewSet for EvaluationCategory model.
    Only Super Admin can modify categories.
    """
    queryset = EvaluationCategory.objects.all()
    serializer_class = EvaluationCategorySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['is_active', 'is_mandatory']
    ordering_fields = ['order', 'name_en']
    ordering = ['order']
    
    def get_permissions(self):
        """Only Super Admin can create, update, or delete categories."""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [permissions.IsAuthenticated]
            # Add custom permission check in perform_* methods
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]
    
    def perform_create(self, serializer):
        """Only Super Admin can create categories."""
        if self.request.user.role != 'SUPER_ADMIN':
            raise permissions.PermissionDenied('Only Super Admin can create categories.')
        serializer.save()
    
    def perform_update(self, serializer):
        """Only Super Admin can update categories."""
        if self.request.user.role != 'SUPER_ADMIN':
            raise permissions.PermissionDenied('Only Super Admin can update categories.')
        
        # Prevent deletion of mandatory categories
        if serializer.instance.is_mandatory and not serializer.validated_data.get('is_active', True):
            raise permissions.PermissionDenied('Mandatory categories cannot be deactivated.')
        
        serializer.save()
    
    def perform_destroy(self, instance):
        """Only Super Admin can delete categories, and not mandatory ones."""
        if self.request.user.role != 'SUPER_ADMIN':
            raise permissions.PermissionDenied('Only Super Admin can delete categories.')
        
        if instance.is_mandatory:
            raise permissions.PermissionDenied('Mandatory categories cannot be deleted.')
        
        instance.delete()


class EvaluationQuestionViewSet(viewsets.ModelViewSet):
    """
    ViewSet for EvaluationQuestion model.
    Only Super Admin can modify questions.
    """
    queryset = EvaluationQuestion.objects.all()
    serializer_class = EvaluationQuestionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['category', 'is_active']
    ordering_fields = ['category__order', 'order']
    ordering = ['category__order', 'order']
    
    def perform_create(self, serializer):
        """Only Super Admin can create questions."""
        if self.request.user.role != 'SUPER_ADMIN':
            raise permissions.PermissionDenied('Only Super Admin can create questions.')
        serializer.save()
    
    def perform_update(self, serializer):
        """Only Super Admin can update questions."""
        if self.request.user.role != 'SUPER_ADMIN':
            raise permissions.PermissionDenied('Only Super Admin can update questions.')
        serializer.save()
    
    def perform_destroy(self, instance):
        """Only Super Admin can delete questions."""
        if self.request.user.role != 'SUPER_ADMIN':
            raise permissions.PermissionDenied('Only Super Admin can delete questions.')
        instance.delete()


class EvaluationViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Evaluation model with role-based access control.
    """
    queryset = Evaluation.objects.all()
    serializer_class = EvaluationSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'evaluator', 'evaluation_period_start', 'evaluation_period_end']
    search_fields = ['employee__english_name', 'employee__arabic_name', 'employee__employee_id']
    ordering_fields = ['created_at', 'evaluation_period_start']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """Filter evaluations based on user's role and access."""
        user = self.request.user
        
        if user.role == 'SUPER_ADMIN':
            return Evaluation.objects.all()
        elif user.role in ['MANAGER', 'SUPERVISOR']:
            # Get evaluations for employees they can evaluate
            accessible_employees = []
            if user.role == 'MANAGER':
                accessible_depts = user.get_accessible_departments()
                accessible_employees = CustomUser.objects.filter(
                    primary_department__in=accessible_depts
                )
            elif user.role == 'SUPERVISOR':
                if user.primary_department:
                    accessible_employees = user.primary_department.get_direct_employees()
            
            return Evaluation.objects.filter(employee__in=accessible_employees)
        elif user.role == 'QUALITY_TEAM':
            return Evaluation.objects.all()  # Read-only access
        else:  # EMPLOYEE
            return Evaluation.objects.filter(employee=user)
    
    @action(detail=True, methods=['post'])
    def submit(self, request, pk=None):
        """Submit evaluation for approval."""
        evaluation = self.get_object()
        
        if evaluation.evaluator != request.user:
            return Response(
                {'error': 'Only the evaluator can submit this evaluation'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        if evaluation.status != 'DRAFT':
            return Response(
                {'error': 'Only draft evaluations can be submitted'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Calculate final score
        evaluation.calculate_score()
        evaluation.status = 'SUBMITTED'
        evaluation.submitted_at = timezone.now()
        evaluation.save()
        
        return Response({'message': 'Evaluation submitted successfully'})
    
    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve submitted evaluation."""
        evaluation = self.get_object()
        
        # Only managers can approve evaluations
        if request.user.role not in ['SUPER_ADMIN', 'MANAGER']:
            return Response(
                {'error': 'Only managers can approve evaluations'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        if evaluation.status != 'SUBMITTED':
            return Response(
                {'error': 'Only submitted evaluations can be approved'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        evaluation.status = 'APPROVED'
        evaluation.approved_at = timezone.now()
        evaluation.approved_by = request.user
        evaluation.save()
        
        return Response({'message': 'Evaluation approved successfully'})
