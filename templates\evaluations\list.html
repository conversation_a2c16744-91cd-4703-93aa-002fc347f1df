{% extends 'base.html' %}

{% block title %}Evaluations - Employee Rating System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Evaluations</h1>
    {% if user.role in 'SUPER_ADMIN,MANAGER,SUPERVISOR' %}
    <a href="{% url 'evaluations:create' %}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        {% if LANGUAGE_CODE == 'ar' %}تقييم جديد{% else %}New Evaluation{% endif %}
    </a>
    {% endif %}
</div>

<div class="card">
    <div class="card-body">
        {% if evaluations %}
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Employee</th>
                        <th>Evaluator</th>
                        <th>Period</th>
                        <th>Status</th>
                        <th>Score</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for evaluation in evaluations %}
                    <tr>
                        <td>{{ evaluation.employee.english_name }}</td>
                        <td>{{ evaluation.evaluator.english_name }}</td>
                        <td>{{ evaluation.evaluation_period_start|date:"M Y" }} - {{ evaluation.evaluation_period_end|date:"M Y" }}</td>
                        <td>
                            {% if evaluation.status == 'DRAFT' %}
                                <span class="badge bg-secondary">Draft</span>
                            {% elif evaluation.status == 'SUBMITTED' %}
                                <span class="badge bg-warning">Submitted</span>
                            {% elif evaluation.status == 'APPROVED' %}
                                <span class="badge bg-success">Approved</span>
                            {% else %}
                                <span class="badge bg-danger">Rejected</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if evaluation.percentage_score %}
                                {{ evaluation.percentage_score|floatformat:1 }}%
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            <a href="{% url 'evaluations:detail' evaluation.pk %}" class="btn btn-sm btn-outline-primary">
                                View
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center">
            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
            <p class="text-muted">No evaluations available.</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
