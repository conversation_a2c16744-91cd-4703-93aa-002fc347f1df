{% extends 'base.html' %}

{% block title %}
    {% if LANGUAGE_CODE == 'ar' %}
        تسجيل حساب جديد - نظام تقييم الموظفين
    {% else %}
        Register - Employee Rating System
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid" style="max-width: 600px;">
    <div class="text-center mb-4">
        <h1 class="h2 text-gray-900 mb-2">
            {% if LANGUAGE_CODE == 'ar' %}
                إنشاء حساب جديد
            {% else %}
                Create New Account
            {% endif %}
        </h1>
        <p class="text-muted">
            {% if LANGUAGE_CODE == 'ar' %}
                املأ النموذج أدناه لطلب إنشاء حساب جديد. سيتم مراجعة طلبك من قبل الإدارة.
            {% else %}
                Fill out the form below to request a new account. Your request will be reviewed by administration.
            {% endif %}
        </p>
    </div>

    <div class="card">
        <div class="card-body p-4">
            <form method="post" novalidate>
                {% csrf_token %}
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="username" class="form-label fw-semibold">
                            {% if LANGUAGE_CODE == 'ar' %}اسم المستخدم{% else %}Username{% endif %}
                            <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="username" name="username" 
                               placeholder="{% if LANGUAGE_CODE == 'ar' %}اختر اسم مستخدم{% else %}Choose a username{% endif %}" required>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="employee_id" class="form-label fw-semibold">
                            {% if LANGUAGE_CODE == 'ar' %}رقم الموظف{% else %}Employee ID{% endif %}
                            <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="employee_id" name="employee_id" 
                               placeholder="{% if LANGUAGE_CODE == 'ar' %}أدخل رقم الموظف{% else %}Enter your employee ID{% endif %}" required>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="email" class="form-label fw-semibold">
                        {% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}Email Address{% endif %}
                        <span class="text-danger">*</span>
                    </label>
                    <input type="email" class="form-control" id="email" name="email" 
                           placeholder="{% if LANGUAGE_CODE == 'ar' %}أدخل بريدك الإلكتروني{% else %}Enter your email address{% endif %}" required>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="english_name" class="form-label fw-semibold">
                            {% if LANGUAGE_CODE == 'ar' %}الاسم بالإنجليزية{% else %}Full Name (English){% endif %}
                            <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="english_name" name="english_name" 
                               placeholder="{% if LANGUAGE_CODE == 'ar' %}الاسم الكامل بالإنجليزية{% else %}Enter your full name in English{% endif %}" required>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="arabic_name" class="form-label fw-semibold">
                            {% if LANGUAGE_CODE == 'ar' %}الاسم بالعربية{% else %}Full Name (Arabic){% endif %}
                        </label>
                        <input type="text" class="form-control" id="arabic_name" name="arabic_name" 
                               placeholder="{% if LANGUAGE_CODE == 'ar' %}الاسم الكامل بالعربية (اختياري){% else %}Enter your full name in Arabic (optional){% endif %}">
                    </div>
                </div>

                <div class="mb-3">
                    <label for="phone_number" class="form-label fw-semibold">
                        {% if LANGUAGE_CODE == 'ar' %}رقم الهاتف{% else %}Phone Number{% endif %}
                    </label>
                    <input type="tel" class="form-control" id="phone_number" name="phone_number" 
                           placeholder="{% if LANGUAGE_CODE == 'ar' %}أدخل رقم هاتفك (اختياري){% else %}Enter your phone number (optional){% endif %}">
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="password" class="form-label fw-semibold">
                            {% if LANGUAGE_CODE == 'ar' %}كلمة المرور{% else %}Password{% endif %}
                            <span class="text-danger">*</span>
                        </label>
                        <input type="password" class="form-control" id="password" name="password" 
                               placeholder="{% if LANGUAGE_CODE == 'ar' %}أدخل كلمة مرور قوية{% else %}Enter a strong password{% endif %}" required>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="confirm_password" class="form-label fw-semibold">
                            {% if LANGUAGE_CODE == 'ar' %}تأكيد كلمة المرور{% else %}Confirm Password{% endif %}
                            <span class="text-danger">*</span>
                        </label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                               placeholder="{% if LANGUAGE_CODE == 'ar' %}أعد إدخال كلمة المرور{% else %}Confirm your password{% endif %}" required>
                    </div>
                </div>

                <div class="alert alert-info mb-4">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                        <strong>ملاحظة:</strong> سيتم مراجعة طلبك من قبل الإدارة وستتلقى إشعاراً عند تفعيل حسابك.
                    {% else %}
                        <strong>Note:</strong> Your request will be reviewed by administration and you'll be notified when your account is activated.
                    {% endif %}
                </div>

                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-user-plus me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}إرسال طلب التسجيل{% else %}Submit Registration Request{% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="text-center mt-4">
        <p class="text-muted">
            {% if LANGUAGE_CODE == 'ar' %}
                لديك حساب بالفعل؟ 
                <a href="{% url 'users:login' %}" class="text-decoration-none">تسجيل الدخول</a>
            {% else %}
                Already have an account? 
                <a href="{% url 'users:login' %}" class="text-decoration-none">Sign In</a>
            {% endif %}
        </p>
    </div>
</div>

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{% endblock %}
