{% extends 'base.html' %}

{% block title %}
    {% if LANGUAGE_CODE == 'ar' %}
        لوحة تحكم المشرف - نظام تقييم الموظفين
    {% else %}
        Supervisor Dashboard - Employee Rating System
    {% endif %}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            {% if LANGUAGE_CODE == 'ar' %}
                لوحة تحكم المشرف
            {% else %}
                Supervisor Dashboard
            {% endif %}
        </h1>
        
        <div class="alert alert-success" role="alert">
            <i class="fas fa-user-check me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}
                مرحباً {{ user.arabic_name|default:user.username }}! يمكنك تقييم وإدارة فريقك المباشر.
            {% else %}
                Welcome {{ user.english_name|default:user.username }}! You can evaluate and manage your direct team.
            {% endif %}
        </div>
    </div>
</div>

<div class="row g-4 mb-4">
    <div class="col-lg-4 col-md-6">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">
                            {% if LANGUAGE_CODE == 'ar' %}أعضاء الفريق{% else %}Team Members{% endif %}
                        </h5>
                        <h2 class="mb-0">{{ team_members|default:0 }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 col-md-6">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">
                            {% if LANGUAGE_CODE == 'ar' %}التقييمات المعلقة{% else %}Pending Evaluations{% endif %}
                        </h5>
                        <h2 class="mb-0">{{ pending_evaluations|default:0 }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 col-md-6">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">
                            {% if LANGUAGE_CODE == 'ar' %}التقييمات المكتملة{% else %}Completed Evaluations{% endif %}
                        </h5>
                        <h2 class="mb-0">{{ completed_evaluations|default:0 }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    {% if LANGUAGE_CODE == 'ar' %}
                        الإجراءات السريعة
                    {% else %}
                        Quick Actions
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <a href="{% url 'evaluations:list' %}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-chart-line me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}عرض التقييمات{% else %}View Evaluations{% endif %}
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="#" class="btn btn-outline-success w-100">
                            <i class="fas fa-plus me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}تقييم جديد{% else %}New Evaluation{% endif %}
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="#" class="btn btn-outline-info w-100">
                            <i class="fas fa-users me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}فريقي{% else %}My Team{% endif %}
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="#" class="btn btn-outline-warning w-100">
                            <i class="fas fa-file-alt me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}تقارير الفريق{% else %}Team Reports{% endif %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    {% if LANGUAGE_CODE == 'ar' %}
                        معلومات سريعة
                    {% else %}
                        Quick Info
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-2">
                    <strong>
                        {% if LANGUAGE_CODE == 'ar' %}الدور:{% else %}Role:{% endif %}
                    </strong>
                    {{ user.get_role_display }}
                </p>
                <p class="mb-2">
                    <strong>
                        {% if LANGUAGE_CODE == 'ar' %}رقم الموظف:{% else %}Employee ID:{% endif %}
                    </strong>
                    {{ user.employee_id }}
                </p>
                <p class="mb-0">
                    <strong>
                        {% if LANGUAGE_CODE == 'ar' %}القسم:{% else %}Department:{% endif %}
                    </strong>
                    {% if LANGUAGE_CODE == 'ar' %}
                        قسم المستخدم
                    {% else %}
                        User Department
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
