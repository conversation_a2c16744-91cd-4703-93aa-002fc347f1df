# Generated by Django 4.2.23 on 2025-07-14 06:04

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('evaluations', '0002_populate_evaluation_data'),
    ]

    operations = [
        migrations.AlterField(
            model_name='evaluationresponse',
            name='score',
            field=models.DecimalField(decimal_places=1, help_text='Numeric score for this question (0-10 with decimal support)', max_digits=4, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(10)], verbose_name='Score'),
        ),
    ]
