"""
Evaluation models for Employee Rating System.
Implements configurable evaluation criteria and bilingual rating system as per PRD.
"""

from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.translation import gettext_lazy as _
from core.models import BaseModel


class ActiveEvaluationManager(models.Manager):
    """Custom manager to exclude soft-deleted evaluations by default."""

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)


class AllEvaluationManager(models.Manager):
    """Manager to include all evaluations (including soft-deleted)."""

    def get_queryset(self):
        return super().get_queryset()


class EvaluationCategory(BaseModel):
    """
    Configurable evaluation categories (e.g., Attendance, Work Volume, etc.).
    Super Admin can modify these dynamically.
    """
    
    name_en = models.CharField(
        _('English Name'),
        max_length=255,
        help_text=_('Category name in English')
    )
    
    name_ar = models.CharField(
        _('Arabic Name (الاسم بالعربية)'),
        max_length=255,
        help_text=_('Category name in Arabic')
    )
    
    weight_percentage = models.DecimalField(
        _('Weight Percentage'),
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text=_('Percentage weight in final score (0-100)')
    )
    
    is_mandatory = models.BooleanField(
        _('Is Mandatory'),
        default=False,
        help_text=_('Whether this category cannot be removed (e.g., Attendance)')
    )
    
    is_active = models.BooleanField(
        _('Active'),
        default=True,
        help_text=_('Whether this category is currently active')
    )
    
    order = models.PositiveIntegerField(
        _('Display Order'),
        default=0,
        help_text=_('Order for displaying categories')
    )
    
    description_en = models.TextField(
        _('English Description'),
        blank=True,
        help_text=_('Category description in English')
    )
    
    description_ar = models.TextField(
        _('Arabic Description (الوصف بالعربية)'),
        blank=True,
        help_text=_('Category description in Arabic')
    )
    
    class Meta:
        verbose_name = _('Evaluation Category')
        verbose_name_plural = _('Evaluation Categories')
        ordering = ['order', 'name_en']
    
    def __str__(self):
        return f"{self.name_en} ({self.weight_percentage}%)"
    
    def get_display_name(self, language='en'):
        """Get display name based on language preference."""
        if language == 'ar':
            return self.name_ar
        return self.name_en


class EvaluationQuestion(BaseModel):
    """
    Individual questions within evaluation categories.
    Super Admin can configure these dynamically.
    """
    
    category = models.ForeignKey(
        EvaluationCategory,
        on_delete=models.CASCADE,
        related_name='questions',
        verbose_name=_('Category')
    )
    
    question_en = models.TextField(
        _('English Question'),
        help_text=_('Question text in English')
    )
    
    question_ar = models.TextField(
        _('Arabic Question (السؤال بالعربية)'),
        help_text=_('Question text in Arabic')
    )
    
    weight_percentage = models.DecimalField(
        _('Weight Percentage'),
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text=_('Percentage weight within category (0-100)')
    )
    
    scale_max = models.PositiveIntegerField(
        _('Maximum Scale Value'),
        default=5,
        validators=[MinValueValidator(1), MaxValueValidator(10)],
        help_text=_('Maximum value for rating scale (e.g., 5 for 1-5 scale)')
    )
    
    is_active = models.BooleanField(
        _('Active'),
        default=True,
        help_text=_('Whether this question is currently active')
    )
    
    order = models.PositiveIntegerField(
        _('Display Order'),
        default=0,
        help_text=_('Order for displaying questions within category')
    )
    
    help_text_en = models.TextField(
        _('English Help Text'),
        blank=True,
        help_text=_('Additional guidance for evaluators in English')
    )
    
    help_text_ar = models.TextField(
        _('Arabic Help Text (النص المساعد بالعربية)'),
        blank=True,
        help_text=_('Additional guidance for evaluators in Arabic')
    )
    
    class Meta:
        verbose_name = _('Evaluation Question')
        verbose_name_plural = _('Evaluation Questions')
        ordering = ['category__order', 'order', 'question_en']
    
    def __str__(self):
        return f"{self.category.name_en}: {self.question_en[:50]}..."
    
    def get_display_question(self, language='en'):
        """Get question text based on language preference."""
        if language == 'ar':
            return self.question_ar
        return self.question_en


class Evaluation(BaseModel):
    """
    Individual employee evaluation instance.
    """

    EVALUATION_STATUS = [
        ('DRAFT', _('Draft')),
        ('SUBMITTED', _('Submitted')),
        ('APPROVED', _('Approved')),
        ('REJECTED', _('Rejected')),
    ]

    # Custom managers
    objects = ActiveEvaluationManager()  # Default manager excludes soft-deleted
    all_objects = AllEvaluationManager()  # Manager that includes soft-deleted
    
    # Who is being evaluated
    employee = models.ForeignKey(
        'users.CustomUser',
        on_delete=models.CASCADE,
        related_name='evaluations',
        verbose_name=_('Employee')
    )
    
    # Who is doing the evaluation
    evaluator = models.ForeignKey(
        'users.CustomUser',
        on_delete=models.CASCADE,
        related_name='conducted_evaluations',
        verbose_name=_('Evaluator')
    )
    
    # Evaluation period
    evaluation_period_start = models.DateField(
        _('Evaluation Period Start'),
        help_text=_('Start date of evaluation period')
    )
    
    evaluation_period_end = models.DateField(
        _('Evaluation Period End'),
        help_text=_('End date of evaluation period')
    )
    
    # Status and workflow
    status = models.CharField(
        _('Status'),
        max_length=20,
        choices=EVALUATION_STATUS,
        default='DRAFT'
    )
    
    # Calculated scores
    total_score = models.DecimalField(
        _('Total Score'),
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_('Calculated total score')
    )
    
    percentage_score = models.DecimalField(
        _('Percentage Score'),
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_('Score as percentage')
    )
    
    # Comments
    overall_comments_en = models.TextField(
        _('Overall Comments (English)'),
        blank=True,
        help_text=_('General comments about performance in English')
    )
    
    overall_comments_ar = models.TextField(
        _('Overall Comments (Arabic)'),
        blank=True,
        help_text=_('General comments about performance in Arabic')
    )
    
    # Workflow dates
    submitted_at = models.DateTimeField(
        _('Submitted At'),
        null=True,
        blank=True
    )
    
    approved_at = models.DateTimeField(
        _('Approved At'),
        null=True,
        blank=True
    )
    
    approved_by = models.ForeignKey(
        'users.CustomUser',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_evaluations',
        verbose_name=_('Approved By')
    )
    
    class Meta:
        verbose_name = _('Evaluation')
        verbose_name_plural = _('Evaluations')
        ordering = ['-created_at']
        unique_together = ['employee', 'evaluation_period_start', 'evaluation_period_end']
    
    def __str__(self):
        return f"{self.employee.english_name} - {self.evaluation_period_start} to {self.evaluation_period_end}"
    
    def calculate_score(self):
        """Calculate total score based on category weights and responses."""
        total_weighted_score = 0
        total_weight = 0
        
        # Get all active categories
        categories = EvaluationCategory.objects.filter(is_active=True)
        
        for category in categories:
            category_score = self.get_category_score(category)
            if category_score is not None:
                weighted_score = category_score * (float(category.weight_percentage) / 100)
                total_weighted_score += weighted_score
                total_weight += float(category.weight_percentage)
        
        if total_weight > 0:
            # Normalize to account for any missing categories
            self.total_score = (total_weighted_score / total_weight) * 100
            self.percentage_score = self.total_score
        else:
            self.total_score = 0
            self.percentage_score = 0
        
        self.save()
        return self.total_score
    
    def get_category_score(self, category):
        """Get average score for a specific category."""
        responses = self.responses.filter(
            question__category=category,
            question__is_active=True
        )
        
        if not responses.exists():
            return None
        
        total_weighted_score = 0
        total_weight = 0
        
        for response in responses:
            # Normalize score to 0-100 scale
            normalized_score = (float(response.score) / float(response.question.scale_max)) * 100
            weight = float(response.question.weight_percentage)
            total_weighted_score += normalized_score * weight
            total_weight += weight
        
        if total_weight > 0:
            return total_weighted_score / total_weight
        return None


class EvaluationResponse(BaseModel):
    """
    Individual responses to evaluation questions.
    """
    
    evaluation = models.ForeignKey(
        Evaluation,
        on_delete=models.CASCADE,
        related_name='responses',
        verbose_name=_('Evaluation')
    )
    
    question = models.ForeignKey(
        EvaluationQuestion,
        on_delete=models.CASCADE,
        related_name='responses',
        verbose_name=_('Question')
    )
    
    score = models.DecimalField(
        _('Score'),
        max_digits=4,
        decimal_places=1,
        validators=[MinValueValidator(0), MaxValueValidator(10)],
        help_text=_('Numeric score for this question (0-10 with decimal support)')
    )
    
    comments_en = models.TextField(
        _('Comments (English)'),
        blank=True,
        help_text=_('Additional comments in English')
    )
    
    comments_ar = models.TextField(
        _('Comments (Arabic)'),
        blank=True,
        help_text=_('Additional comments in Arabic')
    )
    
    class Meta:
        verbose_name = _('Evaluation Response')
        verbose_name_plural = _('Evaluation Responses')
        unique_together = ['evaluation', 'question']
    
    def __str__(self):
        return f"{self.evaluation.employee.english_name} - {self.question.question_en[:30]}... - {self.score}"
    
    def clean(self):
        """Validate that score is within question's scale."""
        from django.core.exceptions import ValidationError
        if self.score > self.question.scale_max:
            raise ValidationError(
                _('Score cannot exceed maximum scale value of %(max)s') % 
                {'max': self.question.scale_max}
            )
