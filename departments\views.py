"""
Views for departments app.
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from .models import Department


@login_required
def department_list(request):
    """List departments accessible to the user."""
    from users.models import CustomUser

    user = request.user
    departments = []

    # Filter departments based on user role
    if user.role == 'SUPER_ADMIN':
        departments = Department.objects.filter(is_active=True)
    elif user.role == 'MANAGER':
        accessible_depts = user.get_accessible_departments()
        departments = accessible_depts
    elif user.role == 'SUPERVISOR':
        # For now, return empty list since department relationships are not fully implemented
        departments = []
    elif user.role == 'QUALITY_TEAM':
        departments = Department.objects.filter(is_active=True)

    # Calculate statistics
    total_employees = CustomUser.objects.filter(is_active=True, role__in=['EMPLOYEE', 'SUPERVISOR', 'MANAGER']).count()
    total_managers = CustomUser.objects.filter(is_active=True, role__in=['MANAGER', 'SUPERVISOR']).count()
    active_departments = departments.filter(is_active=True).count() if hasattr(departments, 'filter') else len([d for d in departments if d.is_active])

    return render(request, 'departments/list.html', {
        'departments': departments,
        'user_role': user.role,
        'total_employees': total_employees,
        'total_managers': total_managers,
        'active_departments': active_departments,
    })


@login_required
def department_detail(request, pk):
    """Department detail view with access control."""
    department = get_object_or_404(Department, pk=pk, is_active=True)
    
    # Check access permissions
    if not department.can_user_access(request.user):
        from django.core.exceptions import PermissionDenied
        raise PermissionDenied(_('You do not have permission to access this department.'))
    
    # Get employees based on user role
    if request.user.role in ['SUPER_ADMIN', 'MANAGER']:
        employees = department.get_all_employees()
    else:
        employees = department.get_direct_employees()

    # Calculate additional statistics
    from evaluations.models import Evaluation
    manager_count = employees.filter(role__in=['MANAGER', 'SUPERVISOR']).count()
    evaluation_count = Evaluation.objects.filter(employee__in=employees).count()

    context = {
        'department': department,
        'employees': employees,
        'employee_count': department.get_employee_count(),
        'manager_count': manager_count,
        'evaluation_count': evaluation_count,
        'can_edit': request.user.role == 'SUPER_ADMIN',
    }
    
    return render(request, 'departments/detail.html', context)


@login_required
def department_hierarchy(request):
    """Department hierarchy visualization."""
    user = request.user
    
    # Get root departments accessible to user
    if user.role == 'SUPER_ADMIN':
        root_departments = Department.objects.filter(parent=None, is_active=True)
    elif user.role == 'MANAGER':
        managed_depts = user.managed_departments.filter(is_active=True)
        root_departments = []
        for dept in managed_depts:
            # Get the root of each managed department
            root = dept.get_root()
            if root not in root_departments:
                root_departments.append(root)
    elif user.role == 'SUPERVISOR':
        # For now, return empty list since department relationships are not fully implemented
        root_departments = []
    elif user.role == 'QUALITY_TEAM':
        root_departments = Department.objects.filter(parent=None, is_active=True)
    else:
        root_departments = []
    
    return render(request, 'departments/hierarchy.html', {
        'root_departments': root_departments,
        'user_role': user.role
    })


@login_required
def department_create(request):
    """Create a new department."""
    if request.user.role not in ['SUPER_ADMIN', 'MANAGER']:
        messages.error(request, _('You do not have permission to create departments.'))
        return redirect('departments:list')

    if request.method == 'POST':
        name_en = request.POST.get('name_en')
        name_ar = request.POST.get('name_ar')
        code = request.POST.get('code')
        parent_id = request.POST.get('parent')
        description_en = request.POST.get('description_en', '')
        description_ar = request.POST.get('description_ar', '')
        is_active = request.POST.get('is_active') == 'on'

        # Validation
        if Department.objects.filter(code=code).exists():
            messages.error(request, _('Department code already exists.'))
            return render(request, 'departments/create.html', {
                'departments': Department.objects.filter(is_active=True)
            })

        try:
            parent = None
            if parent_id:
                parent = Department.objects.get(id=parent_id)

            department = Department.objects.create(
                name_en=name_en,
                name_ar=name_ar,
                code=code,
                parent=parent,
                description_en=description_en,
                description_ar=description_ar,
                is_active=is_active
            )

            messages.success(request, _('Department created successfully.'))
            return redirect('departments:detail', pk=department.pk)

        except Exception as e:
            messages.error(request, _('Error creating department. Please try again.'))

    return render(request, 'departments/create.html', {
        'departments': Department.objects.filter(is_active=True)
    })


@login_required
def department_edit(request, pk):
    """Edit an existing department."""
    department = get_object_or_404(Department, pk=pk)

    if request.user.role not in ['SUPER_ADMIN', 'MANAGER']:
        messages.error(request, _('You do not have permission to edit departments.'))
        return redirect('departments:detail', pk=pk)

    if request.method == 'POST':
        department.name_en = request.POST.get('name_en')
        department.name_ar = request.POST.get('name_ar')
        department.code = request.POST.get('code')
        department.description_en = request.POST.get('description_en', '')
        department.description_ar = request.POST.get('description_ar', '')
        department.is_active = request.POST.get('is_active') == 'on'

        parent_id = request.POST.get('parent')
        if parent_id:
            parent = Department.objects.get(id=parent_id)
            # Prevent circular references
            if parent == department or department in parent.get_ancestors():
                messages.error(request, _('Cannot set parent to self or descendant.'))
                return render(request, 'departments/edit.html', {
                    'department': department,
                    'departments': Department.objects.exclude(pk=department.pk)
                })
            department.parent = parent
        else:
            department.parent = None

        try:
            department.save()
            messages.success(request, _('Department updated successfully.'))
            return redirect('departments:detail', pk=pk)
        except Exception as e:
            messages.error(request, _('Error updating department. Please try again.'))

    return render(request, 'departments/edit.html', {
        'department': department,
        'departments': Department.objects.exclude(pk=department.pk)
    })


@login_required
@require_http_methods(["POST"])
def department_delete(request, pk):
    """Delete a department."""
    department = get_object_or_404(Department, pk=pk)

    if request.user.role not in ['SUPER_ADMIN']:
        messages.error(request, _('You do not have permission to delete departments.'))
        return redirect('departments:detail', pk=pk)

    # Check if department has children
    if department.get_children().exists():
        messages.error(request, _('Cannot delete department with sub-departments.'))
        return redirect('departments:detail', pk=pk)

    try:
        department_name = department.name_en
        department.delete()
        messages.success(request, _(f'Department "{department_name}" deleted successfully.'))
    except Exception as e:
        messages.error(request, _('Error deleting department. Please try again.'))

    return redirect('departments:list')
