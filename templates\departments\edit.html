{% extends 'base.html' %}

{% block title %}
    {% if LANGUAGE_CODE == 'ar' %}
        تعديل {{ department.name_ar }} - نظام تقييم الموظفين
    {% else %}
        Edit {{ department.name_en }} - Employee Rating System
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid" style="max-width: 800px;">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h2 text-gray-900 mb-1">
                {% if LANGUAGE_CODE == 'ar' %}
                    تعديل القسم
                {% else %}
                    Edit Department
                {% endif %}
            </h1>
            <p class="text-muted">
                {% if LANGUAGE_CODE == 'ar' %}
                    تحديث معلومات {{ department.name_ar }}
                {% else %}
                    Update {{ department.name_en }} information
                {% endif %}
            </p>
        </div>
        <div>
            <a href="{% url 'departments:detail' department.pk %}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-eye me-2"></i>
                {% if LANGUAGE_CODE == 'ar' %}عرض{% else %}View{% endif %}
            </a>
            <a href="{% url 'departments:list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                {% if LANGUAGE_CODE == 'ar' %}العودة{% else %}Back{% endif %}
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body p-4">
            <form method="post">
                {% csrf_token %}
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="name_en" class="form-label fw-semibold">
                            {% if LANGUAGE_CODE == 'ar' %}الاسم بالإنجليزية{% else %}English Name{% endif %}
                            <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="name_en" name="name_en" 
                               value="{{ department.name_en }}" required>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="name_ar" class="form-label fw-semibold">
                            {% if LANGUAGE_CODE == 'ar' %}الاسم بالعربية{% else %}Arabic Name{% endif %}
                            <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="name_ar" name="name_ar" 
                               value="{{ department.name_ar }}" required>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="code" class="form-label fw-semibold">
                            {% if LANGUAGE_CODE == 'ar' %}رمز القسم{% else %}Department Code{% endif %}
                            <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="code" name="code" 
                               value="{{ department.code }}" required>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="parent" class="form-label fw-semibold">
                            {% if LANGUAGE_CODE == 'ar' %}القسم الأب{% else %}Parent Department{% endif %}
                        </label>
                        <select class="form-select" id="parent" name="parent">
                            <option value="">
                                {% if LANGUAGE_CODE == 'ar' %}لا يوجد (قسم رئيسي){% else %}None (Root Department){% endif %}
                            </option>
                            {% for dept in departments %}
                            <option value="{{ dept.id }}" {% if department.parent_id == dept.id %}selected{% endif %}>
                                {{ dept.name_en }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description_en" class="form-label fw-semibold">
                        {% if LANGUAGE_CODE == 'ar' %}الوصف بالإنجليزية{% else %}English Description{% endif %}
                    </label>
                    <textarea class="form-control" id="description_en" name="description_en" rows="3">{{ department.description_en }}</textarea>
                </div>

                <div class="mb-3">
                    <label for="description_ar" class="form-label fw-semibold">
                        {% if LANGUAGE_CODE == 'ar' %}الوصف بالعربية{% else %}Arabic Description{% endif %}
                    </label>
                    <textarea class="form-control" id="description_ar" name="description_ar" rows="3">{{ department.description_ar }}</textarea>
                </div>

                <div class="mb-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                               {% if department.is_active %}checked{% endif %}>
                        <label class="form-check-label fw-semibold" for="is_active">
                            {% if LANGUAGE_CODE == 'ar' %}القسم نشط{% else %}Department Active{% endif %}
                        </label>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{% url 'departments:detail' department.pk %}" class="btn btn-outline-secondary me-md-2">
                        {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}Cancel{% endif %}
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}حفظ التغييرات{% else %}Save Changes{% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
