{% extends 'base.html' %}

{% block title %}
    {% if LANGUAGE_CODE == 'ar' %}
        لوحة تحكم مدير النظام - نظام تقييم الموظفين
    {% else %}
        Super Admin Dashboard - Employee Rating System
    {% endif %}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            {% if LANGUAGE_CODE == 'ar' %}
                لوحة تحكم مدير النظام
            {% else %}
                Super Admin Dashboard
            {% endif %}
        </h1>
        
        <div class="alert alert-info" role="alert">
            <i class="fas fa-crown me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}
                مرحباً {{ user.arabic_name|default:user.username }}! لديك صلاحيات كاملة لإدارة النظام.
            {% else %}
                Welcome {{ user.english_name|default:user.username }}! You have full system administration privileges.
            {% endif %}
        </div>
    </div>
</div>

<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="stat-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="text-muted mb-1">
                        {% if LANGUAGE_CODE == 'ar' %}الأقسام{% else %}Departments{% endif %}
                    </h5>
                    <h2 class="mb-0 text-primary">{{ total_departments|default:0 }}</h2>
                    <a href="{% url 'departments:list' %}" class="text-decoration-none small">
                        {% if LANGUAGE_CODE == 'ar' %}عرض الأقسام{% else %}View Departments{% endif %}
                        <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-sitemap text-white"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">
                            {% if LANGUAGE_CODE == 'ar' %}المستخدمون{% else %}Users{% endif %}
                        </h5>
                        <h2 class="mb-0">{{ total_users|default:0 }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="/admin/users/customuser/" class="text-white text-decoration-none">
                    {% if LANGUAGE_CODE == 'ar' %}إدارة المستخدمين{% else %}Manage Users{% endif %}
                    <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">
                            {% if LANGUAGE_CODE == 'ar' %}التقييمات{% else %}Evaluations{% endif %}
                        </h5>
                        <h2 class="mb-0">{{ total_evaluations|default:0 }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{% url 'evaluations:list' %}" class="text-white text-decoration-none">
                    {% if LANGUAGE_CODE == 'ar' %}عرض التقييمات{% else %}View Evaluations{% endif %}
                    <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">
                            {% if LANGUAGE_CODE == 'ar' %}معايير التقييم{% else %}Categories{% endif %}
                        </h5>
                        <h2 class="mb-0">{{ total_categories|default:0 }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-cogs fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="/admin/evaluations/evaluationcategory/" class="text-white text-decoration-none">
                    {% if LANGUAGE_CODE == 'ar' %}إدارة المعايير{% else %}Manage Categories{% endif %}
                    <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    {% if LANGUAGE_CODE == 'ar' %}
                        الإجراءات السريعة
                    {% else %}
                        Quick Actions
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <a href="/admin/departments/department/add/" class="btn btn-outline-primary w-100">
                            <i class="fas fa-plus me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}إضافة قسم جديد{% else %}Add New Department{% endif %}
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="/admin/users/customuser/add/" class="btn btn-outline-success w-100">
                            <i class="fas fa-user-plus me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}إضافة مستخدم جديد{% else %}Add New User{% endif %}
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="/admin/evaluations/evaluationcategory/add/" class="btn btn-outline-warning w-100">
                            <i class="fas fa-tags me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}إضافة معيار تقييم{% else %}Add Evaluation Category{% endif %}
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="{% url 'departments:hierarchy' %}" class="btn btn-outline-info w-100">
                            <i class="fas fa-sitemap me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}عرض الهيكل التنظيمي{% else %}View Organization Chart{% endif %}
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="{% url 'users:pending_users' %}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-user-clock me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}طلبات التسجيل المعلقة{% else %}Pending User Requests{% endif %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    {% if LANGUAGE_CODE == 'ar' %}
                        روابط الإدارة
                    {% else %}
                        Admin Links
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="/admin/" class="list-group-item list-group-item-action">
                        <i class="fas fa-cog me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}لوحة الإدارة الرئيسية{% else %}Main Admin Panel{% endif %}
                    </a>
                    <a href="/admin/users/customuser/" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}إدارة المستخدمين{% else %}User Management{% endif %}
                    </a>
                    <a href="/admin/departments/department/" class="list-group-item list-group-item-action">
                        <i class="fas fa-sitemap me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}إدارة الأقسام{% else %}Department Management{% endif %}
                    </a>
                    <a href="/admin/evaluations/evaluationcategory/" class="list-group-item list-group-item-action">
                        <i class="fas fa-tags me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}معايير التقييم{% else %}Evaluation Categories{% endif %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
