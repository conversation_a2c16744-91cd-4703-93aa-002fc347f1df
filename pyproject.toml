[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "employee-rating-system"
version = "2.0.0"
description = "Bilingual Employee Performance Evaluation System with Hierarchical Access Control"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "Development Team", email = "<EMAIL>"},
]
keywords = ["django", "employee", "evaluation", "rating", "bilingual", "arabic", "english"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Environment :: Web Environment",
    "Framework :: Django",
    "Framework :: Django :: 4.2",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Internet :: WWW/HTTP",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
    "Topic :: Office/Business :: Human Resources",
]
requires-python = ">=3.8"
dependencies = [
    "Django>=4.2.7,<5.0",
    "djangorestframework>=3.14.0",
    "python-decouple>=3.8",
    "psycopg2-binary>=2.9.7",
    "django-mptt>=0.14.0",
    "redis>=5.0.1",
    "celery>=5.3.4",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-django>=4.7.0",
    "pytest-cov>=4.1.0",
    "factory-boy>=3.3.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "django-debug-toolbar>=4.2.0",
    "django-extensions>=3.2.3",
]
prod = [
    "gunicorn>=21.2.0",
    "whitenoise>=6.6.0",
    "sentry-sdk>=1.38.0",
]

[project.urls]
Homepage = "https://github.com/company/employee-rating-system"
Documentation = "https://employee-rating-docs.company.com"
Repository = "https://github.com/company/employee-rating-system.git"
"Bug Tracker" = "https://github.com/company/employee-rating-system/issues"

[tool.setuptools]
packages = ["employee_rating", "core", "users", "departments", "evaluations", "api"]

[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_django = "django"
known_first_party = ["employee_rating", "core", "users", "departments", "evaluations", "api"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "DJANGO", "FIRSTPARTY", "LOCALFOLDER"]
skip = ["migrations"]

[tool.flake8]
max-line-length = 88
exclude = [
    ".git",
    "__pycache__",
    "migrations",
    ".venv",
    "venv",
    ".tox",
    "build",
    "dist",
    "*.egg-info",
]
ignore = [
    "E203",  # whitespace before ':'
    "E501",  # line too long (handled by black)
    "W503",  # line break before binary operator
]

[tool.coverage.run]
source = "."
omit = [
    "*/migrations/*",
    "*/venv/*",
    "*/env/*",
    "manage.py",
    "*/settings/*",
    "*/tests/*",
    "*/__pycache__/*",
    "*/static/*",
    "*/media/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.mypy]
python_version = "3.8"
check_untyped_defs = true
ignore_missing_imports = true
warn_unused_ignores = true
warn_redundant_casts = true
warn_unused_configs = true
exclude = [
    "migrations/",
    "venv/",
    "env/",
]
