"""
Middleware for core app.
"""

from django.utils import translation
from django.utils.deprecation import MiddlewareMixin
from django.shortcuts import redirect
from django.urls import reverse
from django.contrib import messages
from django.utils.translation import gettext_lazy as _


class UserLanguageMiddleware(MiddlewareMixin):
    """
    Middleware to set user's preferred language.
    """
    
    def process_request(self, request):
        """Set language based on user preference or URL parameter."""
        
        # Handle language switching via URL parameter
        if 'lang' in request.GET:
            language = request.GET['lang']
            if language in ['en', 'ar']:
                # Save to user preference if authenticated
                if hasattr(request, 'user') and request.user.is_authenticated:
                    request.user.language_preference = language
                    request.user.save(update_fields=['language_preference'])
                
                # Set session language
                request.session['django_language'] = language
                translation.activate(language)
                
                # Redirect to clean URL with proper language prefix
                redirect_url = request.path

                # Remove existing language prefix if present
                if redirect_url.startswith('/ar/'):
                    redirect_url = redirect_url[3:]
                elif redirect_url.startswith('/en/'):
                    redirect_url = redirect_url[3:]

                # Add new language prefix if Arabic
                if language == 'ar':
                    redirect_url = '/ar' + redirect_url

                # Handle query parameters
                if request.GET:
                    params = request.GET.copy()
                    del params['lang']
                    if params:
                        redirect_url += '?' + params.urlencode()

                response = redirect(redirect_url)
                response.set_cookie('django_language', language, max_age=365*24*60*60)  # 1 year
                return response
        
        # Set language based on user preference
        language = None

        # 1. Check user preference (if authenticated)
        if hasattr(request, 'user') and request.user.is_authenticated and hasattr(request.user, 'language_preference'):
            language = request.user.language_preference
        
        # 2. Check session
        if not language:
            language = request.session.get('django_language')
        
        # 3. Check cookie
        if not language:
            language = request.COOKIES.get('django_language')
        
        # 4. Default to English
        if not language or language not in ['en', 'ar']:
            language = 'en'
        
        # Activate language
        translation.activate(language)
        request.LANGUAGE_CODE = language
        
        # Set session for consistency
        request.session['django_language'] = language


class LanguageSwitchView:
    """
    View to handle language switching.
    """
    
    @staticmethod
    def switch_language(request):
        """Switch user language preference."""
        if request.method == 'POST':
            language = request.POST.get('language')
            if language in ['en', 'ar']:
                # Save to user preference if authenticated
                if hasattr(request, 'user') and request.user.is_authenticated:
                    request.user.language_preference = language
                    request.user.save(update_fields=['language_preference'])
                    messages.success(request, _('Language preference updated successfully.'))
                
                # Set session language
                request.session['django_language'] = language
                translation.activate(language)
                
                # Set cookie
                response = redirect(request.META.get('HTTP_REFERER', '/'))
                response.set_cookie('django_language', language, max_age=365*24*60*60)
                return response
        
        return redirect('core:home')
