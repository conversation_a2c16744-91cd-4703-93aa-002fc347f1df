# Generated by Django 4.2.23 on 2025-07-13 18:45

from django.db import migrations


def populate_evaluation_data(apps, schema_editor):
    """Populate evaluation categories and questions based on PRD requirements."""
    EvaluationCategory = apps.get_model('evaluations', 'EvaluationCategory')
    EvaluationQuestion = apps.get_model('evaluations', 'EvaluationQuestion')

    # Create evaluation categories as per PRD
    categories_data = [
        {
            'name_en': 'Attendance & Punctuality',
            'name_ar': 'الحضور والانصراف',
            'weight_percentage': 25.00,
            'is_mandatory': True,
            'order': 1
        },
        {
            'name_en': 'Work Volume',
            'name_ar': 'حجم العمل',
            'weight_percentage': 35.00,
            'is_mandatory': False,
            'order': 2
        },
        {
            'name_en': 'Creative Work',
            'name_ar': 'العمل الإبداعي',
            'weight_percentage': 20.00,
            'is_mandatory': False,
            'order': 3
        },
        {
            'name_en': 'Direct Supervisor Evaluation',
            'name_ar': 'تقييم المسؤول المباشر',
            'weight_percentage': 20.00,
            'is_mandatory': False,
            'order': 4
        }
    ]

    # Create categories
    categories = {}
    for cat_data in categories_data:
        category = EvaluationCategory.objects.create(**cat_data)
        categories[cat_data['name_en']] = category

    # Create evaluation questions for each category
    questions_data = [
        # Attendance & Punctuality Questions
        {
            'category': 'Attendance & Punctuality',
            'question_en': 'Number of attendance days (Days present vs. total working days)',
            'question_ar': 'عدد أيام الحضور (الأيام الحاضرة مقابل إجمالي أيام العمل)',
            'weight_percentage': 40.00,
            'order': 1
        },
        {
            'category': 'Attendance & Punctuality',
            'question_en': 'Total hours worked (Based on 7:15 hours per day standard)',
            'question_ar': 'إجمالي ساعات العمل (بناءً على معيار 7:15 ساعة يومياً)',
            'weight_percentage': 30.00,
            'order': 2
        },
        {
            'category': 'Attendance & Punctuality',
            'question_en': 'Punctuality rate (On-time arrival and departure)',
            'question_ar': 'معدل الالتزام بالمواعيد (الوصول والمغادرة في الوقت المحدد)',
            'weight_percentage': 20.00,
            'order': 3
        },
        {
            'category': 'Attendance & Punctuality',
            'question_en': 'Unplanned absence frequency (Emergency leaves and sick days)',
            'question_ar': 'تكرار الغياب غير المخطط (الإجازات الطارئة وأيام المرض)',
            'weight_percentage': 10.00,
            'order': 4
        },

        # Work Volume Questions
        {
            'category': 'Work Volume',
            'question_en': 'Work quality volume (Quality of output relative to quantity)',
            'question_ar': 'حجم العمل في الجودة (جودة المخرجات نسبة إلى الكمية)',
            'weight_percentage': 50.00,
            'order': 1
        },
        {
            'category': 'Work Volume',
            'question_en': 'Monthly work volume (Total output and productivity metrics)',
            'question_ar': 'حجم العمل الشهري (إجمالي المخرجات ومقاييس الإنتاجية)',
            'weight_percentage': 30.00,
            'order': 2
        },
        {
            'category': 'Work Volume',
            'question_en': 'Task completion rate (Percentage of assigned tasks completed)',
            'question_ar': 'معدل إنجاز المهام (نسبة المهام المكلفة المكتملة)',
            'weight_percentage': 20.00,
            'order': 3
        },

        # Creative Work Questions
        {
            'category': 'Creative Work',
            'question_en': 'Innovation and new ideas (Original solutions and creative approaches)',
            'question_ar': 'الابتكار والأفكار الجديدة (الحلول الأصلية والمناهج الإبداعية)',
            'weight_percentage': 30.00,
            'order': 1
        },
        {
            'category': 'Creative Work',
            'question_en': 'Process improvements (Efficiency enhancements and optimization)',
            'question_ar': 'تحسين العمليات (تحسينات الكفاءة والتحسين)',
            'weight_percentage': 25.00,
            'order': 2
        },
        {
            'category': 'Creative Work',
            'question_en': 'Problem-solving approach (Creative problem resolution methods)',
            'question_ar': 'أسلوب حل المشاكل (طرق حل المشاكل الإبداعية)',
            'weight_percentage': 25.00,
            'order': 3
        },
        {
            'category': 'Creative Work',
            'question_en': 'Initiative taken (Proactive behavior and self-direction)',
            'question_ar': 'المبادرة المتخذة (السلوك الاستباقي والتوجيه الذاتي)',
            'weight_percentage': 20.00,
            'order': 4
        },

        # Direct Supervisor Evaluation Questions
        {
            'category': 'Direct Supervisor Evaluation',
            'question_en': 'Communication style (Effectiveness in communication and interpersonal skills)',
            'question_ar': 'أسلوب التواصل (الفعالية في التواصل والمهارات الشخصية)',
            'weight_percentage': 50.00,
            'order': 1
        },
        {
            'category': 'Direct Supervisor Evaluation',
            'question_en': 'Collaboration (Teamwork, cooperation, and relationship building)',
            'question_ar': 'التعاون (العمل الجماعي والتعاون وبناء العلاقات)',
            'weight_percentage': 30.00,
            'order': 2
        },
        {
            'category': 'Direct Supervisor Evaluation',
            'question_en': 'Professional behavior (Adherence to company values and professional standards)',
            'question_ar': 'السلوك المهني (الالتزام بقيم الشركة والمعايير المهنية)',
            'weight_percentage': 20.00,
            'order': 3
        }
    ]

    # Create questions
    for q_data in questions_data:
        category_name = q_data.pop('category')
        category = categories[category_name]
        EvaluationQuestion.objects.create(
            category=category,
            **q_data
        )


def reverse_populate_evaluation_data(apps, schema_editor):
    """Remove evaluation data."""
    EvaluationCategory = apps.get_model('evaluations', 'EvaluationCategory')
    EvaluationQuestion = apps.get_model('evaluations', 'EvaluationQuestion')

    EvaluationQuestion.objects.all().delete()
    EvaluationCategory.objects.all().delete()


class Migration(migrations.Migration):

    dependencies = [
        ('evaluations', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(populate_evaluation_data, reverse_populate_evaluation_data),
    ]
