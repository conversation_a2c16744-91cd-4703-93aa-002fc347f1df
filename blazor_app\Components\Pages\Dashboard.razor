@page "/dashboard"
@using System.Globalization
@inject IJSRuntime JSRuntime

<PageTitle>@GetPageTitle()</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h2 text-gray-900 mb-1">
            @if (IsArabic())
            {
                <text>لوحة التحكم</text>
            }
            else
            {
                <text>Dashboard</text>
            }
        </h1>
        <p class="text-muted">
            @if (IsArabic())
            {
                <text>نظرة عامة على نظام تقييم الموظفين</text>
            }
            else
            {
                <text>Overview of the Employee Rating System</text>
            }
        </p>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon text-white me-3">
                    <i class="fas fa-building"></i>
                </div>
                <div>
                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                        @if (IsArabic())
                        {
                            <text>إجمالي الأقسام</text>
                        }
                        else
                        {
                            <text>Total Departments</text>
                        }
                    </div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800">@totalDepartments</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon text-white me-3">
                    <i class="fas fa-users"></i>
                </div>
                <div>
                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                        @if (IsArabic())
                        {
                            <text>إجمالي الموظفين</text>
                        }
                        else
                        {
                            <text>Total Employees</text>
                        }
                    </div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800">@totalUsers</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon text-white me-3">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div>
                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                        @if (IsArabic())
                        {
                            <text>إجمالي التقييمات</text>
                        }
                        else
                        {
                            <text>Total Evaluations</text>
                        }
                    </div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800">@totalEvaluations</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon text-white me-3">
                    <i class="fas fa-star"></i>
                </div>
                <div>
                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                        @if (IsArabic())
                        {
                            <text>فئات التقييم</text>
                        }
                        else
                        {
                            <text>Evaluation Categories</text>
                        }
                    </div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800">@totalCategories</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    @if (IsArabic())
                    {
                        <text>الإجراءات السريعة</text>
                    }
                    else
                    {
                        <text>Quick Actions</text>
                    }
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/evaluations/create" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        @if (IsArabic())
                        {
                            <text>إنشاء تقييم جديد</text>
                        }
                        else
                        {
                            <text>Create New Evaluation</text>
                        }
                    </a>
                    <a href="/departments" class="btn btn-outline-primary">
                        <i class="fas fa-building me-2"></i>
                        @if (IsArabic())
                        {
                            <text>إدارة الأقسام</text>
                        }
                        else
                        {
                            <text>Manage Departments</text>
                        }
                    </a>
                    <a href="/users" class="btn btn-outline-primary">
                        <i class="fas fa-users me-2"></i>
                        @if (IsArabic())
                        {
                            <text>إدارة المستخدمين</text>
                        }
                        else
                        {
                            <text>Manage Users</text>
                        }
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    @if (IsArabic())
                    {
                        <text>معلومات النظام</text>
                    }
                    else
                    {
                        <text>System Information</text>
                    }
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-6">
                        <p class="mb-2">
                            <strong>
                                @if (IsArabic())
                                {
                                    <text>الإصدار:</text>
                                }
                                else
                                {
                                    <text>Version:</text>
                                }
                            </strong>
                        </p>
                        <p class="text-muted">2.0.0</p>
                    </div>
                    <div class="col-sm-6">
                        <p class="mb-2">
                            <strong>
                                @if (IsArabic())
                                {
                                    <text>اللغة:</text>
                                }
                                else
                                {
                                    <text>Language:</text>
                                }
                            </strong>
                        </p>
                        <p class="text-muted">@GetCurrentLanguage().ToUpper()</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private int totalDepartments = 0;
    private int totalUsers = 0;
    private int totalEvaluations = 0;
    private int totalCategories = 0;

    protected override async Task OnInitializedAsync()
    {
        // TODO: Load actual data from database
        // For now, using sample data
        totalDepartments = 5;
        totalUsers = 25;
        totalEvaluations = 120;
        totalCategories = 4;
    }

    private string GetCurrentLanguage()
    {
        return CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
    }

    private bool IsArabic()
    {
        return GetCurrentLanguage() == "ar";
    }

    private string GetPageTitle()
    {
        return IsArabic() ? "لوحة التحكم - نظام تقييم الموظفين" : "Dashboard - Employee Rating System";
    }
}
