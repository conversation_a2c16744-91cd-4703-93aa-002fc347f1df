@page "/dashboard"
@using System.Globalization
@inject IJSRuntime JSRuntime

<PageTitle>@GetPageTitle()</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h2 text-gray-900 mb-1">
            @if (IsArabic())
            {
                <text>لوحة التحكم</text>
            }
            else
            {
                <text>Dashboard</text>
            }
        </h1>
        <p class="text-muted">
            @if (IsArabic())
            {
                <text>نظرة عامة على نظام تقييم الموظفين</text>
            }
            else
            {
                <text>Overview of the Employee Rating System</text>
            }
        </p>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon text-white me-3">
                    <i class="fas fa-building"></i>
                </div>
                <div>
                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                        @if (IsArabic())
                        {
                            <text>إجمالي الأقسام</text>
                        }
                        else
                        {
                            <text>Total Departments</text>
                        }
                    </div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800">@totalDepartments</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon text-white me-3">
                    <i class="fas fa-users"></i>
                </div>
                <div>
                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                        @if (IsArabic())
                        {
                            <text>إجمالي الموظفين</text>
                        }
                        else
                        {
                            <text>Total Employees</text>
                        }
                    </div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800">@totalUsers</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon text-white me-3">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div>
                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                        @if (IsArabic())
                        {
                            <text>إجمالي التقييمات</text>
                        }
                        else
                        {
                            <text>Total Evaluations</text>
                        }
                    </div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800">@totalEvaluations</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon text-white me-3">
                    <i class="fas fa-star"></i>
                </div>
                <div>
                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                        @if (IsArabic())
                        {
                            <text>فئات التقييم</text>
                        }
                        else
                        {
                            <text>Evaluation Categories</text>
                        }
                    </div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800">@totalCategories</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    @if (IsArabic())
                    {
                        <text>الإجراءات السريعة</text>
                    }
                    else
                    {
                        <text>Quick Actions</text>
                    }
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/evaluations/create" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        @if (IsArabic())
                        {
                            <text>إنشاء تقييم جديد</text>
                        }
                        else
                        {
                            <text>Create New Evaluation</text>
                        }
                    </a>
                    <a href="/departments" class="btn btn-outline-primary">
                        <i class="fas fa-building me-2"></i>
                        @if (IsArabic())
                        {
                            <text>إدارة الأقسام</text>
                        }
                        else
                        {
                            <text>Manage Departments</text>
                        }
                    </a>
                    <a href="/users" class="btn btn-outline-primary">
                        <i class="fas fa-users me-2"></i>
                        @if (IsArabic())
                        {
                            <text>إدارة المستخدمين</text>
                        }
                        else
                        {
                            <text>Manage Users</text>
                        }
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    @if (IsArabic())
                    {
                        <text>معلومات النظام</text>
                    }
                    else
                    {
                        <text>System Information</text>
                    }
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-6">
                        <p class="mb-2">
                            <strong>
                                @if (IsArabic())
                                {
                                    <text>الإصدار:</text>
                                }
                                else
                                {
                                    <text>Version:</text>
                                }
                            </strong>
                        </p>
                        <p class="text-muted">2.0.0</p>
                    </div>
                    <div class="col-sm-6">
                        <p class="mb-2">
                            <strong>
                                @if (IsArabic())
                                {
                                    <text>اللغة:</text>
                                }
                                else
                                {
                                    <text>Language:</text>
                                }
                            </strong>
                        </p>
                        <p class="text-muted">@GetCurrentLanguage().ToUpper()</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Department Performance Overview -->
<div class="row mb-4">
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    @if (IsArabic())
                    {
                        <text>أداء الأقسام</text>
                    }
                    else
                    {
                        <text>Department Performance</text>
                    }
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>
                                    @if (IsArabic())
                                    {
                                        <text>القسم</text>
                                    }
                                    else
                                    {
                                        <text>Department</text>
                                    }
                                </th>
                                <th class="text-center">
                                    @if (IsArabic())
                                    {
                                        <text>عدد الموظفين</text>
                                    }
                                    else
                                    {
                                        <text>Employees</text>
                                    }
                                </th>
                                <th class="text-center">
                                    @if (IsArabic())
                                    {
                                        <text>التقييمات</text>
                                    }
                                    else
                                    {
                                        <text>Evaluations</text>
                                    }
                                </th>
                                <th class="text-center">
                                    @if (IsArabic())
                                    {
                                        <text>متوسط الأداء</text>
                                    }
                                    else
                                    {
                                        <text>Avg Performance</text>
                                    }
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var dept in departmentStats)
                            {
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-building text-primary me-2"></i>
                                            <span class="fw-bold">@dept.DepartmentName</span>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-info">@dept.EmployeeCount</span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-secondary">@dept.EvaluationCount</span>
                                    </td>
                                    <td class="text-center">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <div class="progress me-2" style="width: 60px; height: 8px;">
                                                <div class="progress-bar @GetPerformanceClass(dept.AverageScore)"
                                                     style="width: @dept.AverageScore%"></div>
                                            </div>
                                            <span class="fw-bold @GetPerformanceTextClass(dept.AverageScore)">
                                                @dept.AverageScore.ToString("F1")%
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    @if (IsArabic())
                    {
                        <text>إحصائيات سريعة</text>
                    }
                    else
                    {
                        <text>Quick Stats</text>
                    }
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="text-muted">
                            @if (IsArabic())
                            {
                                <text>التقييمات المعتمدة</text>
                            }
                            else
                            {
                                <text>Approved Evaluations</text>
                            }
                        </span>
                        <span class="fw-bold">@completedEvaluations</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-success" style="width: @((decimal)completedEvaluations / totalEvaluations * 100)%"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="text-muted">
                            @if (IsArabic())
                            {
                                <text>قيد المراجعة</text>
                            }
                            else
                            {
                                <text>Pending Review</text>
                            }
                        </span>
                        <span class="fw-bold">@pendingEvaluations</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-warning" style="width: @((decimal)pendingEvaluations / totalEvaluations * 100)%"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="text-muted">
                            @if (IsArabic())
                            {
                                <text>متوسط الأداء العام</text>
                            }
                            else
                            {
                                <text>Overall Performance</text>
                            }
                        </span>
                        <span class="fw-bold text-primary">@averageScore.ToString("F1")%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar @GetPerformanceClass(averageScore)" style="width: @averageScore%"></div>
                    </div>
                </div>

                <hr>

                <div class="text-center">
                    <a href="/reports" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-chart-bar me-1"></i>
                        @if (IsArabic())
                        {
                            <text>عرض التقارير التفصيلية</text>
                        }
                        else
                        {
                            <text>View Detailed Reports</text>
                        }
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Evaluations -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                @if (IsArabic())
                {
                    <text>التقييمات الأخيرة</text>
                }
                else
                {
                    <text>Recent Evaluations</text>
                }
            </h6>
            <a href="/evaluations" class="btn btn-outline-primary btn-sm">
                @if (IsArabic())
                {
                    <text>عرض الكل</text>
                }
                else
                {
                    <text>View All</text>
                }
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>
                            @if (IsArabic())
                            {
                                <text>الموظف</text>
                            }
                            else
                            {
                                <text>Employee</text>
                            }
                        </th>
                        <th>
                            @if (IsArabic())
                            {
                                <text>المقيم</text>
                            }
                            else
                            {
                                <text>Evaluator</text>
                            }
                        </th>
                        <th class="text-center">
                            @if (IsArabic())
                            {
                                <text>النتيجة</text>
                            }
                            else
                            {
                                <text>Score</text>
                            }
                        </th>
                        <th class="text-center">
                            @if (IsArabic())
                            {
                                <text>الحالة</text>
                            }
                            else
                            {
                                <text>Status</text>
                            }
                        </th>
                        <th class="text-center">
                            @if (IsArabic())
                            {
                                <text>التاريخ</text>
                            }
                            else
                            {
                                <text>Date</text>
                            }
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var evaluation in recentEvaluations)
                    {
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="profile-avatar me-2">
                                        @evaluation.EmployeeName[0]
                                    </div>
                                    <span class="fw-bold">@evaluation.EmployeeName</span>
                                </div>
                            </td>
                            <td>@evaluation.EvaluatorName</td>
                            <td class="text-center">
                                <span class="fw-bold @GetPerformanceTextClass(evaluation.Score)">
                                    @evaluation.Score.ToString("F1")%
                                </span>
                            </td>
                            <td class="text-center">
                                <span class="badge @GetStatusBadgeClass(evaluation.Status)">
                                    @GetStatusDisplayName(evaluation.Status)
                                </span>
                            </td>
                            <td class="text-center">
                                <small class="text-muted">@evaluation.CreatedAt.ToString("dd/MM/yyyy")</small>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

@code {
    private int totalDepartments = 0;
    private int totalUsers = 0;
    private int totalEvaluations = 0;
    private int totalCategories = 0;
    private int pendingEvaluations = 0;
    private int completedEvaluations = 0;
    private decimal averageScore = 0;
    private List<DepartmentStats> departmentStats = new();
    private List<RecentEvaluation> recentEvaluations = new();

    public class DepartmentStats
    {
        public string DepartmentName { get; set; } = string.Empty;
        public int EmployeeCount { get; set; }
        public int EvaluationCount { get; set; }
        public decimal AverageScore { get; set; }
    }

    public class RecentEvaluation
    {
        public string EmployeeName { get; set; } = string.Empty;
        public string EvaluatorName { get; set; } = string.Empty;
        public decimal Score { get; set; }
        public DateTime CreatedAt { get; set; }
        public EvaluationStatus Status { get; set; }
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        // Load actual data from database
        // For demo purposes, using sample data with realistic values
        totalDepartments = 8;
        totalUsers = 45;
        totalEvaluations = 156;
        totalCategories = 6;
        pendingEvaluations = 12;
        completedEvaluations = 134;
        averageScore = 82.5m;

        // Sample department statistics
        departmentStats = new List<DepartmentStats>
        {
            new() { DepartmentName = IsArabic() ? "تكنولوجيا المعلومات" : "Information Technology", EmployeeCount = 12, EvaluationCount = 36, AverageScore = 85.2m },
            new() { DepartmentName = IsArabic() ? "الموارد البشرية" : "Human Resources", EmployeeCount = 8, EvaluationCount = 24, AverageScore = 88.1m },
            new() { DepartmentName = IsArabic() ? "المالية" : "Finance", EmployeeCount = 10, EvaluationCount = 30, AverageScore = 79.8m },
            new() { DepartmentName = IsArabic() ? "التسويق" : "Marketing", EmployeeCount = 15, EvaluationCount = 45, AverageScore = 83.7m }
        };

        // Sample recent evaluations
        recentEvaluations = new List<RecentEvaluation>
        {
            new() { EmployeeName = IsArabic() ? "أحمد محمد" : "Ahmed Mohamed", EvaluatorName = IsArabic() ? "سارة أحمد" : "Sarah Ahmed", Score = 92.5m, CreatedAt = DateTime.Now.AddDays(-1), Status = EvaluationStatus.APPROVED },
            new() { EmployeeName = IsArabic() ? "فاطمة علي" : "Fatima Ali", EvaluatorName = IsArabic() ? "محمد حسن" : "Mohamed Hassan", Score = 87.3m, CreatedAt = DateTime.Now.AddDays(-2), Status = EvaluationStatus.SUBMITTED },
            new() { EmployeeName = IsArabic() ? "خالد سالم" : "Khalid Salem", EvaluatorName = IsArabic() ? "نورا إبراهيم" : "Nora Ibrahim", Score = 78.9m, CreatedAt = DateTime.Now.AddDays(-3), Status = EvaluationStatus.APPROVED },
            new() { EmployeeName = IsArabic() ? "مريم عبدالله" : "Mariam Abdullah", EvaluatorName = IsArabic() ? "عمر فاروق" : "Omar Farouk", Score = 95.1m, CreatedAt = DateTime.Now.AddDays(-4), Status = EvaluationStatus.APPROVED },
            new() { EmployeeName = IsArabic() ? "يوسف كريم" : "Youssef Karim", EvaluatorName = IsArabic() ? "ليلى حسام" : "Layla Hossam", Score = 81.7m, CreatedAt = DateTime.Now.AddDays(-5), Status = EvaluationStatus.SUBMITTED }
        };
    }

    private string GetPerformanceClass(decimal score)
    {
        return score >= 90 ? "bg-success" : score >= 80 ? "bg-info" : score >= 70 ? "bg-warning" : "bg-danger";
    }

    private string GetPerformanceTextClass(decimal score)
    {
        return score >= 90 ? "text-success" : score >= 80 ? "text-info" : score >= 70 ? "text-warning" : "text-danger";
    }

    private string GetStatusBadgeClass(EvaluationStatus status)
    {
        return status switch
        {
            EvaluationStatus.DRAFT => "bg-secondary",
            EvaluationStatus.SUBMITTED => "bg-warning",
            EvaluationStatus.APPROVED => "bg-success",
            EvaluationStatus.REJECTED => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetStatusDisplayName(EvaluationStatus status)
    {
        return status switch
        {
            EvaluationStatus.DRAFT => IsArabic() ? "مسودة" : "Draft",
            EvaluationStatus.SUBMITTED => IsArabic() ? "مرسل" : "Submitted",
            EvaluationStatus.APPROVED => IsArabic() ? "معتمد" : "Approved",
            EvaluationStatus.REJECTED => IsArabic() ? "مرفوض" : "Rejected",
            _ => status.ToString()
        };
    }

    private string GetCurrentLanguage()
    {
        return CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
    }

    private bool IsArabic()
    {
        return GetCurrentLanguage() == "ar";
    }

    private string GetPageTitle()
    {
        return IsArabic() ? "لوحة التحكم - نظام تقييم الموظفين" : "Dashboard - Employee Rating System";
    }
}
