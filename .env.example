# Employee Rating System Environment Configuration
# Copy this file to .env and update the values according to your setup

# =============================================================================
# GENERAL SETTINGS
# =============================================================================
DEBUG=True
SECRET_KEY=your-secret-key-here-change-in-production
ALLOWED_HOSTS=localhost,127.0.0.1

# =============================================================================
# DATABASE CONFIGURATION
# Choose one: sqlite, postgresql, oracle
# =============================================================================
DATABASE_ENGINE=sqlite

# SQLite Configuration (Default for development)
# No additional configuration needed for SQLite

# PostgreSQL Configuration
# Uncomment and configure if using PostgreSQL
# POSTGRES_DB=employee_rating
# POSTGRES_USER=postgres
# POSTGRES_PASSWORD=your_password
# POSTGRES_HOST=localhost
# POSTGRES_PORT=5432

# Oracle Configuration
# Uncomment and configure if using Oracle
# ORACLE_DB_NAME=xe
# ORACLE_USER=employee_rating
# ORACLE_PASSWORD=your_password
# ORACLE_HOST=localhost
# ORACLE_PORT=1521
# ORACLE_SERVICE_NAME=xe

# =============================================================================
# INTERNATIONALIZATION
# =============================================================================
LANGUAGE_CODE=en
TIME_ZONE=Asia/Riyadh
USE_I18N=True
USE_L10N=True
USE_TZ=True

# =============================================================================
# STATIC AND MEDIA FILES
# =============================================================================
STATIC_URL=/static/
MEDIA_URL=/media/
STATIC_ROOT=staticfiles/
MEDIA_ROOT=media/

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password

# =============================================================================
# REDIS CONFIGURATION (for caching and sessions)
# =============================================================================
REDIS_URL=redis://localhost:6379/0

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
SECURE_HSTS_INCLUDE_SUBDOMAINS=False
SECURE_HSTS_PRELOAD=False
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True
X_FRAME_OPTIONS=DENY

# =============================================================================
# LOGGING
# =============================================================================
LOG_LEVEL=INFO
LOG_FILE=logs/employee_rating.log

# =============================================================================
# CELERY CONFIGURATION (for background tasks)
# =============================================================================
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
