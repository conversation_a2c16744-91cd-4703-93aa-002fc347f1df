/* Professional Design System - Inspired by React Layout */
:root {
    --primary-gradient: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
    --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --card-shadow-hover: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --border-radius: 0.75rem;
    --border-color: #e5e7eb;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --bg-gray-50: #f9fafb;
}

body {
    background-color: var(--bg-gray-50);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: var(--text-primary);
}

/* Card System */
.card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease-in-out;
}

.card:hover {
    box-shadow: var(--card-shadow-hover);
}

.card-header {
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* Buttons */
.btn-primary {
    background: var(--primary-gradient);
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-outline-primary {
    border: 1px solid #3b82f6;
    color: #3b82f6;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn-outline-primary:hover {
    background: var(--primary-gradient);
    border-color: transparent;
    transform: translateY(-1px);
}

/* Stats Cards */
.stat-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    padding: 1.5rem;
    transition: all 0.2s ease-in-out;
}

.stat-card:hover {
    box-shadow: var(--card-shadow-hover);
    transform: translateY(-2px);
}

.stat-icon {
    background: var(--primary-gradient);
    border-radius: 0.5rem;
    padding: 0.75rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Navigation */
.navbar {
    background: white !important;
    box-shadow: var(--card-shadow);
    border-bottom: 1px solid var(--border-color);
}

.navbar-brand {
    font-weight: 700;
    color: var(--text-primary) !important;
    font-size: 1.25rem;
}

.nav-link {
    color: var(--text-secondary) !important;
    font-weight: 500;
    transition: color 0.2s ease-in-out;
}

.nav-link:hover {
    color: #3b82f6 !important;
}

/* Profile Avatar */
.profile-avatar {
    background: var(--primary-gradient);
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

/* Alert System */
.alert {
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.alert-success {
    background-color: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

.alert-info {
    background-color: #eff6ff;
    border-color: #bfdbfe;
    color: #1e40af;
}

.alert-warning {
    background-color: #fffbeb;
    border-color: #fed7aa;
    color: #92400e;
}

/* Form Elements */
.form-control {
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease-in-out;
}

.form-control:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Tables */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th {
    background-color: var(--bg-gray-50);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    color: var(--text-primary);
}

/* Footer */
.footer {
    margin-top: 3rem;
    padding: 2rem 0;
    background-color: white;
    border-top: 1px solid var(--border-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .stat-card {
        padding: 1rem;
    }
}

/* Arabic RTL Support - Class-based approach for better CSS compatibility */
.rtl-layout {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

.rtl-layout .navbar-nav {
    flex-direction: row-reverse;
}

.rtl-layout .me-2,
.rtl-layout .me-3 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

.rtl-layout .ms-1 {
    margin-right: 0.25rem !important;
    margin-left: 0 !important;
}

.rtl-layout .text-end {
    text-align: left !important;
}

.rtl-layout .dropdown-menu {
    right: 0;
    left: auto;
}

/* Fix button spacing in RTL */
.rtl-layout .btn + .btn {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Additional RTL improvements */
.rtl-layout .d-flex {
    flex-direction: row-reverse;
}

.rtl-layout .justify-content-between {
    flex-direction: row-reverse;
}

.rtl-layout .gap-2 > * + * {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* RTL-specific card and stat improvements */
.rtl-layout .stat-card .d-flex {
    flex-direction: row-reverse;
}

.rtl-layout .card-header .d-flex {
    flex-direction: row-reverse;
}

/* Page layout */
.page {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

main {
    flex: 1;
}

/* Blazor specific styles */
#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    position: absolute;
    right: 0.75rem;
    top: 0.5rem;
}
