{% extends 'base.html' %}

{% block title %}
    {% if LANGUAGE_CODE == 'ar' %}
        الصفحة الرئيسية - نظام تقييم الموظفين
    {% else %}
        Home - Employee Rating System
    {% endif %}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="text-center mb-5">
            <h1 class="display-4 mb-4">
                {% if LANGUAGE_CODE == 'ar' %}
                    مرحباً بك في نظام تقييم الموظفين
                {% else %}
                    Welcome to Employee Rating System
                {% endif %}
            </h1>
            <p class="lead">
                {% if LANGUAGE_CODE == 'ar' %}
                    نظام شامل ثنائي اللغة لتقييم أداء الموظفين مع هيكل إداري هرمي وتحكم في الوصول حسب الأدوار
                {% else %}
                    A comprehensive bilingual employee performance evaluation system with hierarchical structure and role-based access control
                {% endif %}
            </p>
        </div>

        {% if not user.is_authenticated %}
        <div class="row g-4 mb-5">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">
                            {% if LANGUAGE_CODE == 'ar' %}
                                إدارة هرمية للأقسام
                            {% else %}
                                Hierarchical Department Management
                            {% endif %}
                        </h5>
                        <p class="card-text">
                            {% if LANGUAGE_CODE == 'ar' %}
                                دعم هيكل تنظيمي غير محدود المستويات مع علاقات متعددة بين المستخدمين والأقسام
                            {% else %}
                                Unlimited organizational hierarchy with many-to-many user-department relationships
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                        <h5 class="card-title">
                            {% if LANGUAGE_CODE == 'ar' %}
                                تحكم في الوصول حسب الأدوار
                            {% else %}
                                Role-Based Access Control
                            {% endif %}
                        </h5>
                        <p class="card-text">
                            {% if LANGUAGE_CODE == 'ar' %}
                                أدوار متعددة: مدير النظام، مدير القسم، المشرف، فريق الجودة، والموظف
                            {% else %}
                                Multiple roles: Super Admin, Manager, Supervisor, Quality Team, and Employee
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-4 mb-5">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-3x text-warning mb-3"></i>
                        <h5 class="card-title">
                            {% if LANGUAGE_CODE == 'ar' %}
                                نظام تقييم قابل للتخصيص
                            {% else %}
                                Configurable Evaluation System
                            {% endif %}
                        </h5>
                        <p class="card-text">
                            {% if LANGUAGE_CODE == 'ar' %}
                                معايير تقييم ديناميكية مع أوزان قابلة للتعديل ودعم كامل للغتين العربية والإنجليزية
                            {% else %}
                                Dynamic evaluation criteria with adjustable weights and full Arabic/English support
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-globe fa-3x text-info mb-3"></i>
                        <h5 class="card-title">
                            {% if LANGUAGE_CODE == 'ar' %}
                                دعم ثنائي اللغة
                            {% else %}
                                Bilingual Support
                            {% endif %}
                        </h5>
                        <p class="card-text">
                            {% if LANGUAGE_CODE == 'ar' %}
                                واجهة كاملة باللغتين العربية والإنجليزية مع دعم اتجاه النص من اليمين إلى اليسار
                            {% else %}
                                Complete Arabic and English interface with RTL/LTR text direction support
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center">
            <a href="{% url 'users:login' %}" class="btn btn-primary btn-lg me-3">
                {% if LANGUAGE_CODE == 'ar' %}
                    تسجيل الدخول
                {% else %}
                    Login to System
                {% endif %}
            </a>
            <a href="{% url 'users:register' %}" class="btn btn-outline-primary btn-lg me-3">
                {% if LANGUAGE_CODE == 'ar' %}
                    إنشاء حساب جديد
                {% else %}
                    Create Account
                {% endif %}
            </a>
            <a href="/admin/" class="btn btn-outline-secondary btn-lg">
                {% if LANGUAGE_CODE == 'ar' %}
                    لوحة الإدارة
                {% else %}
                    Admin Panel
                {% endif %}
            </a>
        </div>
        {% else %}
        <div class="text-center">
            <div class="alert alert-success" role="alert">
                <h4 class="alert-heading">
                    {% if LANGUAGE_CODE == 'ar' %}
                        مرحباً {{ user.arabic_name|default:user.username }}!
                    {% else %}
                        Welcome {{ user.english_name|default:user.username }}!
                    {% endif %}
                </h4>
                <p>
                    {% if LANGUAGE_CODE == 'ar' %}
                        دورك في النظام: {{ user.get_role_display }}
                    {% else %}
                        Your role: {{ user.get_role_display }}
                    {% endif %}
                </p>
                <hr>
                <a href="{% url 'core:dashboard' %}" class="btn btn-success">
                    {% if LANGUAGE_CODE == 'ar' %}
                        الذهاب إلى لوحة التحكم
                    {% else %}
                        Go to Dashboard
                    {% endif %}
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{% endblock %}
