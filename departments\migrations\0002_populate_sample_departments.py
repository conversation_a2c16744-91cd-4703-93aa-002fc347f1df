# Generated by Django 4.2.23 on 2025-07-13 18:46

from django.db import migrations


def populate_sample_departments(apps, schema_editor):
    """Create a realistic organizational hierarchy."""
    Department = apps.get_model('departments', 'Department')

    # Create root company using insert_node to properly handle MPTT
    company = Department(
        name_en="TechCorp International",
        name_ar="شركة تك كورب الدولية",
        code="TECHCORP",
        description_en="Main company headquarters",
        description_ar="المقر الرئيسي للشركة",
        is_active=True
    )
    # Use insert_node for MPTT compatibility
    try:
        company.insert_at(target=None, position='first-child', save=True)
    except:
        # Fallback to regular save if insert_at is not available in migration
        company.save()
        # Rebuild tree after all insertions
        Department.objects.rebuild()

    # Create main divisions
    divisions_data = [
        {
            'name_en': 'Information Technology Division',
            'name_ar': 'قسم تقنية المعلومات',
            'code': 'IT_DIV',
            'description_en': 'Technology infrastructure and software development',
            'description_ar': 'البنية التحتية التقنية وتطوير البرمجيات'
        },
        {
            'name_en': 'Human Resources Division',
            'name_ar': 'قسم الموارد البشرية',
            'code': 'HR_DIV',
            'description_en': 'Employee management and organizational development',
            'description_ar': 'إدارة الموظفين والتطوير التنظيمي'
        },
        {
            'name_en': 'Sales & Marketing Division',
            'name_ar': 'قسم المبيعات والتسويق',
            'code': 'SALES_DIV',
            'description_en': 'Customer acquisition and revenue generation',
            'description_ar': 'اكتساب العملاء وتوليد الإيرادات'
        },
        {
            'name_en': 'Operations Division',
            'name_ar': 'قسم العمليات',
            'code': 'OPS_DIV',
            'description_en': 'Daily operations and process management',
            'description_ar': 'العمليات اليومية وإدارة العمليات'
        },
        {
            'name_en': 'Finance & Accounting Division',
            'name_ar': 'قسم المالية والمحاسبة',
            'code': 'FIN_DIV',
            'description_en': 'Financial planning and accounting operations',
            'description_ar': 'التخطيط المالي والعمليات المحاسبية'
        }
    ]

    divisions = {}
    for div_data in divisions_data:
        division = Department(**div_data)
        try:
            division.insert_at(target=company, position='last-child', save=True)
        except:
            division.parent = company
            division.save()
        divisions[div_data['code']] = division

    # Create IT sub-departments
    it_departments = [
        {
            'name_en': 'Software Development',
            'name_ar': 'تطوير البرمجيات',
            'code': 'IT_DEV',
            'description_en': 'Application and system development',
            'description_ar': 'تطوير التطبيقات والأنظمة'
        },
        {
            'name_en': 'Infrastructure & Support',
            'name_ar': 'البنية التحتية والدعم',
            'code': 'IT_INFRA',
            'description_en': 'Network, servers, and technical support',
            'description_ar': 'الشبكة والخوادم والدعم التقني'
        },
        {
            'name_en': 'Quality Assurance',
            'name_ar': 'ضمان الجودة',
            'code': 'IT_QA',
            'description_en': 'Software testing and quality control',
            'description_ar': 'اختبار البرمجيات ومراقبة الجودة'
        }
    ]

    for dept_data in it_departments:
        dept = Department(**dept_data)
        try:
            dept.insert_at(target=divisions['IT_DIV'], position='last-child', save=True)
        except:
            dept.parent = divisions['IT_DIV']
            dept.save()

    # Create HR sub-departments
    hr_departments = [
        {
            'name_en': 'Recruitment & Talent Acquisition',
            'name_ar': 'التوظيف واكتساب المواهب',
            'code': 'HR_RECRUIT',
            'description_en': 'Employee recruitment and onboarding',
            'description_ar': 'توظيف الموظفين والإعداد'
        },
        {
            'name_en': 'Training & Development',
            'name_ar': 'التدريب والتطوير',
            'code': 'HR_TRAIN',
            'description_en': 'Employee training and skill development',
            'description_ar': 'تدريب الموظفين وتطوير المهارات'
        },
        {
            'name_en': 'Employee Relations',
            'name_ar': 'علاقات الموظفين',
            'code': 'HR_REL',
            'description_en': 'Employee engagement and relations',
            'description_ar': 'مشاركة الموظفين والعلاقات'
        }
    ]

    for dept_data in hr_departments:
        dept = Department(**dept_data)
        try:
            dept.insert_at(target=divisions['HR_DIV'], position='last-child', save=True)
        except:
            dept.parent = divisions['HR_DIV']
            dept.save()

    # Create Sales sub-departments
    sales_departments = [
        {
            'name_en': 'Regional Sales',
            'name_ar': 'المبيعات الإقليمية',
            'code': 'SALES_REG',
            'description_en': 'Regional sales operations',
            'description_ar': 'عمليات المبيعات الإقليمية'
        },
        {
            'name_en': 'Digital Marketing',
            'name_ar': 'التسويق الرقمي',
            'code': 'SALES_DIGITAL',
            'description_en': 'Online marketing and digital campaigns',
            'description_ar': 'التسويق عبر الإنترنت والحملات الرقمية'
        },
        {
            'name_en': 'Customer Success',
            'name_ar': 'نجاح العملاء',
            'code': 'SALES_CS',
            'description_en': 'Customer retention and success management',
            'description_ar': 'الاحتفاظ بالعملاء وإدارة النجاح'
        }
    ]

    for dept_data in sales_departments:
        dept = Department(**dept_data)
        try:
            dept.insert_at(target=divisions['SALES_DIV'], position='last-child', save=True)
        except:
            dept.parent = divisions['SALES_DIV']
            dept.save()

    # Rebuild the tree to ensure proper MPTT structure
    try:
        Department.objects.rebuild()
    except:
        pass


def reverse_populate_sample_departments(apps, schema_editor):
    """Remove sample departments."""
    Department = apps.get_model('departments', 'Department')
    Department.objects.all().delete()


class Migration(migrations.Migration):

    dependencies = [
        ('departments', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(populate_sample_departments, reverse_populate_sample_departments),
    ]
