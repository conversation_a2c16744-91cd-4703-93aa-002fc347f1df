# Generated by Django 4.2.23 on 2025-07-13 18:47

from django.db import migrations
from django.contrib.auth.hashers import make_password


def populate_sample_users(apps, schema_editor):
    """Create sample users for demo purposes."""
    CustomUser = apps.get_model('users', 'CustomUser')
    Department = apps.get_model('departments', 'Department')

    # Get departments for assignment
    try:
        company = Department.objects.get(code='TECHCORP')
        it_div = Department.objects.get(code='IT_DIV')
        hr_div = Department.objects.get(code='HR_DIV')
        sales_div = Department.objects.get(code='SALES_DIV')
        ops_div = Department.objects.get(code='OPS_DIV')
        fin_div = Department.objects.get(code='FIN_DIV')

        it_dev = Department.objects.get(code='IT_DEV')
        it_infra = Department.objects.get(code='IT_INFRA')
        it_qa = Department.objects.get(code='IT_QA')
        hr_recruit = Department.objects.get(code='HR_RECRUIT')
        hr_train = Department.objects.get(code='HR_TRAIN')
        sales_reg = Department.objects.get(code='SALES_REG')
        sales_digital = Department.objects.get(code='SALES_DIGITAL')
    except Department.DoesNotExist:
        # If departments don't exist, skip user creation
        return

    # Sample users data
    users_data = [
        # Super Admin
        {
            'username': 'superadmin',
            'email': '<EMAIL>',
            'password': 'admin123',
            'employee_id': 'EMP001',
            'english_name': 'System Administrator',
            'arabic_name': 'مدير النظام',
            'role': 'SUPER_ADMIN',
            'is_staff': True,
            'is_superuser': True,
            'phone_number': '******-0001',
            'language_preference': 'en'
        },

        # Division Managers
        {
            'username': 'it_manager',
            'email': '<EMAIL>',
            'password': 'manager123',
            'employee_id': 'EMP002',
            'english_name': 'Ahmed Hassan',
            'arabic_name': 'أحمد حسن',
            'role': 'MANAGER',
            'is_staff': True,
            'phone_number': '******-0002',
            'language_preference': 'ar'
        },
        {
            'username': 'hr_manager',
            'email': '<EMAIL>',
            'password': 'manager123',
            'employee_id': 'EMP003',
            'english_name': 'Sarah Johnson',
            'arabic_name': 'سارة جونسون',
            'role': 'MANAGER',
            'is_staff': True,
            'phone_number': '******-0003',
            'language_preference': 'en'
        },
        {
            'username': 'sales_manager',
            'email': '<EMAIL>',
            'password': 'manager123',
            'employee_id': 'EMP004',
            'english_name': 'Omar Al-Rashid',
            'arabic_name': 'عمر الراشد',
            'role': 'MANAGER',
            'is_staff': True,
            'phone_number': '******-0004',
            'language_preference': 'ar'
        },

        # Department Supervisors
        {
            'username': 'dev_supervisor',
            'email': '<EMAIL>',
            'password': 'supervisor123',
            'employee_id': 'EMP005',
            'english_name': 'Michael Chen',
            'arabic_name': 'مايكل تشين',
            'role': 'SUPERVISOR',
            'phone_number': '******-0005',
            'language_preference': 'en'
        },
        {
            'username': 'qa_supervisor',
            'email': '<EMAIL>',
            'password': 'supervisor123',
            'employee_id': 'EMP006',
            'english_name': 'Fatima Al-Zahra',
            'arabic_name': 'فاطمة الصياح',
            'role': 'SUPERVISOR',
            'phone_number': '******-0006',
            'language_preference': 'ar'
        },
        {
            'username': 'hr_supervisor',
            'email': '<EMAIL>',
            'password': 'supervisor123',
            'employee_id': 'EMP007',
            'english_name': 'David Rodriguez',
            'arabic_name': 'ديفيد رودريغيز',
            'role': 'SUPERVISOR',
            'phone_number': '******-0007',
            'language_preference': 'en'
        },

        # Quality Team Members
        {
            'username': 'quality_lead',
            'email': '<EMAIL>',
            'password': 'quality123',
            'employee_id': 'EMP008',
            'english_name': 'Layla Mahmoud',
            'arabic_name': 'ليلى محمود',
            'role': 'QUALITY_TEAM',
            'phone_number': '******-0008',
            'language_preference': 'ar'
        },
        {
            'username': 'quality_analyst',
            'email': '<EMAIL>',
            'password': 'quality123',
            'employee_id': 'EMP009',
            'english_name': 'James Wilson',
            'arabic_name': 'جيمس ويلسون',
            'role': 'QUALITY_TEAM',
            'phone_number': '******-0009',
            'language_preference': 'en'
        },

        # Employees
        {
            'username': 'dev_senior',
            'email': '<EMAIL>',
            'password': 'employee123',
            'employee_id': 'EMP010',
            'english_name': 'Ali Mansour',
            'arabic_name': 'علي منصور',
            'role': 'EMPLOYEE',
            'phone_number': '******-0010',
            'language_preference': 'ar'
        },
        {
            'username': 'dev_junior',
            'email': '<EMAIL>',
            'password': 'employee123',
            'employee_id': 'EMP011',
            'english_name': 'Emma Thompson',
            'arabic_name': 'إيما تومسون',
            'role': 'EMPLOYEE',
            'phone_number': '******-0011',
            'language_preference': 'en'
        },
        {
            'username': 'qa_tester',
            'email': '<EMAIL>',
            'password': 'employee123',
            'employee_id': 'EMP012',
            'english_name': 'Youssef Ibrahim',
            'arabic_name': 'يوسف إبراهيم',
            'role': 'EMPLOYEE',
            'phone_number': '******-0012',
            'language_preference': 'ar'
        },
        {
            'username': 'hr_specialist',
            'email': '<EMAIL>',
            'password': 'employee123',
            'employee_id': 'EMP013',
            'english_name': 'Lisa Anderson',
            'arabic_name': 'ليزا أندرسون',
            'role': 'EMPLOYEE',
            'phone_number': '******-0013',
            'language_preference': 'en'
        },
        {
            'username': 'sales_rep',
            'email': '<EMAIL>',
            'password': 'employee123',
            'employee_id': 'EMP014',
            'english_name': 'Khalid Al-Otaibi',
            'arabic_name': 'خالد بدر',
            'role': 'EMPLOYEE',
            'phone_number': '******-0014',
            'language_preference': 'ar'
        },
        {
            'username': 'marketing_specialist',
            'email': '<EMAIL>',
            'password': 'employee123',
            'employee_id': 'EMP015',
            'english_name': 'Jennifer Lee',
            'arabic_name': 'جينيفر لي',
            'role': 'EMPLOYEE',
            'phone_number': '******-0015',
            'language_preference': 'en'
        }
    ]

    # Create users
    created_users = {}
    for user_data in users_data:
        password = user_data.pop('password')
        user = CustomUser.objects.create(
            password=make_password(password),
            **user_data
        )
        created_users[user.username] = user


def reverse_populate_sample_users(apps, schema_editor):
    """Remove sample users."""
    CustomUser = apps.get_model('users', 'CustomUser')
    # Don't delete the original admin user, only the sample users
    sample_usernames = [
        'superadmin', 'it_manager', 'hr_manager', 'sales_manager',
        'dev_supervisor', 'qa_supervisor', 'hr_supervisor',
        'quality_lead', 'quality_analyst',
        'dev_senior', 'dev_junior', 'qa_tester', 'hr_specialist',
        'sales_rep', 'marketing_specialist'
    ]
    CustomUser.objects.filter(username__in=sample_usernames).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0002_customuser_language_preference'),
        ('departments', '0002_populate_sample_departments'),
    ]

    operations = [
        migrations.RunPython(populate_sample_users, reverse_populate_sample_users),
    ]
