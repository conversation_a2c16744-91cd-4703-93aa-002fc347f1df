"""
Views for users app.
"""

from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.admin.views.decorators import staff_member_required
from django.core.paginator import Paginator
from .models import CustomUser
from departments.models import Department


@login_required
def profile(request):
    """User profile view."""
    return render(request, 'users/profile.html', {'user': request.user})


def login_view(request):
    """Login view."""
    if request.user.is_authenticated:
        return redirect('core:dashboard')

    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)
            messages.success(request, _('Welcome back!'))
            return redirect('core:dashboard')
        else:
            messages.error(request, _('Invalid username or password.'))

    return render(request, 'users/login.html')


def logout_view(request):
    """Logout view."""
    logout(request)
    messages.success(request, _('You have been successfully logged out.'))
    return redirect('core:home')


def register_view(request):
    """User registration view."""
    if request.user.is_authenticated:
        return redirect('core:dashboard')

    if request.method == 'POST':
        # Get form data
        username = request.POST.get('username')
        email = request.POST.get('email')
        employee_id = request.POST.get('employee_id')
        english_name = request.POST.get('english_name')
        arabic_name = request.POST.get('arabic_name')
        phone_number = request.POST.get('phone_number')
        password = request.POST.get('password')
        confirm_password = request.POST.get('confirm_password')

        # Basic validation
        if password != confirm_password:
            messages.error(request, _('Passwords do not match.'))
            return render(request, 'users/register.html')

        if CustomUser.objects.filter(username=username).exists():
            messages.error(request, _('Username already exists.'))
            return render(request, 'users/register.html')

        if CustomUser.objects.filter(email=email).exists():
            messages.error(request, _('Email already exists.'))
            return render(request, 'users/register.html')

        if CustomUser.objects.filter(employee_id=employee_id).exists():
            messages.error(request, _('Employee ID already exists.'))
            return render(request, 'users/register.html')

        try:
            # Create user with EMPLOYEE role by default
            user = CustomUser.objects.create_user(
                username=username,
                email=email,
                password=password,
                employee_id=employee_id,
                english_name=english_name,
                arabic_name=arabic_name,
                phone_number=phone_number,
                role='EMPLOYEE',
                is_active=False  # Require admin approval
            )

            messages.success(request, _(
                'Registration successful! Your account is pending admin approval. '
                'You will be notified once your account is activated.'
            ))
            return redirect('users:login')

        except Exception as e:
            messages.error(request, _('Registration failed. Please try again.'))

    return render(request, 'users/register.html')


@staff_member_required
def pending_users_view(request):
    """View for admins to see pending user registrations."""
    pending_users = CustomUser.objects.filter(is_active=False).order_by('-date_joined')

    paginator = Paginator(pending_users, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    departments = Department.objects.all().order_by('name_en')

    context = {
        'pending_users': page_obj,
        'departments': departments,
        'total_pending': pending_users.count(),
    }

    return render(request, 'users/pending_users.html', context)


@staff_member_required
@require_http_methods(["POST"])
def approve_user(request, user_id):
    """Approve a pending user registration."""
    user = get_object_or_404(CustomUser, id=user_id, is_active=False)

    # Get department assignments
    department_ids = request.POST.getlist('departments')
    primary_department_id = request.POST.get('primary_department')
    role = request.POST.get('role', 'EMPLOYEE')

    # Activate user
    user.is_active = True
    user.role = role
    user.save()

    # Assign departments (will be implemented when department relationships are added)
    # if department_ids:
    #     departments = Department.objects.filter(id__in=department_ids)
    #     user.departments.set(departments)

        # Set primary department (will be implemented when department relationships are added)
        # if primary_department_id and primary_department_id in department_ids:
        #     primary_dept = Department.objects.get(id=primary_department_id)
        #     user.primary_department = primary_dept
        #     user.save()

    messages.success(request, _(f'User {user.username} has been approved and activated.'))

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({'status': 'success', 'message': 'User approved successfully'})

    return redirect('users:pending_users')


@staff_member_required
@require_http_methods(["POST"])
def reject_user(request, user_id):
    """Reject a pending user registration."""
    user = get_object_or_404(CustomUser, id=user_id, is_active=False)
    username = user.username
    user.delete()

    messages.success(request, _(f'User registration for {username} has been rejected and deleted.'))

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({'status': 'success', 'message': 'User rejected successfully'})

    return redirect('users:pending_users')
