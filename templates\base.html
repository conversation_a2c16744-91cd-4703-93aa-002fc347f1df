<!DOCTYPE html>
<html lang="{% if LANGUAGE_CODE == 'ar' %}ar{% else %}en{% endif %}" dir="{% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Employee Rating System - نظام تقييم الموظفين{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS for RTL support -->
    {% if LANGUAGE_CODE == 'ar' %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% endif %}
    
    <style>
        /* Professional Design System - Inspired by React Layout */
        :root {
            --primary-gradient: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
            --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --card-shadow-hover: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --border-radius: 0.75rem;
            --border-color: #e5e7eb;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --bg-gray-50: #f9fafb;
        }

        body {
            background-color: var(--bg-gray-50);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--text-primary);
        }

        /* Card System */
        .card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            border: 1px solid var(--border-color);
            transition: all 0.2s ease-in-out;
        }

        .card:hover {
            box-shadow: var(--card-shadow-hover);
        }

        .card-header {
            border-bottom: 1px solid var(--border-color);
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        /* Buttons */
        .btn-primary {
            background: var(--primary-gradient);
            border: none;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.2s ease-in-out;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        .btn-outline-primary {
            border: 1px solid #3b82f6;
            color: #3b82f6;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.2s ease-in-out;
        }

        .btn-outline-primary:hover {
            background: var(--primary-gradient);
            border-color: transparent;
            transform: translateY(-1px);
        }

        /* Stats Cards */
        .stat-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            border: 1px solid var(--border-color);
            padding: 1.5rem;
            transition: all 0.2s ease-in-out;
        }

        .stat-card:hover {
            box-shadow: var(--card-shadow-hover);
            transform: translateY(-2px);
        }

        .stat-icon {
            background: var(--primary-gradient);
            border-radius: 0.5rem;
            padding: 0.75rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        /* Navigation */
        .navbar {
            background: white !important;
            box-shadow: var(--card-shadow);
            border-bottom: 1px solid var(--border-color);
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--text-primary) !important;
            font-size: 1.25rem;
        }

        .nav-link {
            color: var(--text-secondary) !important;
            font-weight: 500;
            transition: color 0.2s ease-in-out;
        }

        .nav-link:hover {
            color: #3b82f6 !important;
        }

        /* Profile Avatar */
        .profile-avatar {
            background: var(--primary-gradient);
            border-radius: 50%;
            width: 2.5rem;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        /* Alert System */
        .alert {
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }

        .alert-success {
            background-color: #f0fdf4;
            border-color: #bbf7d0;
            color: #166534;
        }

        .alert-info {
            background-color: #eff6ff;
            border-color: #bfdbfe;
            color: #1e40af;
        }

        .alert-warning {
            background-color: #fffbeb;
            border-color: #fed7aa;
            color: #92400e;
        }

        /* Form Elements */
        .form-control {
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
            transition: all 0.2s ease-in-out;
        }

        .form-control:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Tables */
        .table {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .table th {
            background-color: var(--bg-gray-50);
            border-bottom: 1px solid var(--border-color);
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Footer */
        .footer {
            margin-top: 3rem;
            padding: 2rem 0;
            background-color: white;
            border-top: 1px solid var(--border-color);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .stat-card {
                padding: 1rem;
            }
        }

        /* Arabic RTL Support - Class-based approach for better CSS compatibility */
        .rtl-layout {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
        }

        .rtl-layout .navbar-nav {
            flex-direction: row-reverse;
        }

        .rtl-layout .me-2,
        .rtl-layout .me-3 {
            margin-left: 0.5rem !important;
            margin-right: 0 !important;
        }

        .rtl-layout .ms-1 {
            margin-right: 0.25rem !important;
            margin-left: 0 !important;
        }

        .rtl-layout .text-end {
            text-align: left !important;
        }

        .rtl-layout .dropdown-menu {
            right: 0;
            left: auto;
        }

        /* Fix button spacing in RTL */
        .rtl-layout .btn + .btn {
            margin-left: 0.5rem;
            margin-right: 0;
        }

        /* Additional RTL improvements */
        .rtl-layout .d-flex {
            flex-direction: row-reverse;
        }

        .rtl-layout .justify-content-between {
            flex-direction: row-reverse;
        }

        .rtl-layout .gap-2 > * + * {
            margin-left: 0.5rem;
            margin-right: 0;
        }

        /* RTL-specific card and stat improvements */
        .rtl-layout .stat-card .d-flex {
            flex-direction: row-reverse;
        }

        .rtl-layout .card-header .d-flex {
            flex-direction: row-reverse;
        }
    </style>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body class="{% if LANGUAGE_CODE == 'ar' %}rtl-layout{% endif %}">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white">
        <div class="container">
            <a class="navbar-brand" href="{% url 'core:home' %}">
                {% if LANGUAGE_CODE == 'ar' %}
                    نظام تقييم الموظفين
                {% else %}
                    Employee Rating System
                {% endif %}
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if user.is_authenticated %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'core:dashboard' %}">
                                {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}Dashboard{% endif %}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'departments:list' %}">
                                {% if LANGUAGE_CODE == 'ar' %}الأقسام{% else %}Departments{% endif %}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'evaluations:list' %}">
                                {% if LANGUAGE_CODE == 'ar' %}التقييمات{% else %}Evaluations{% endif %}
                            </a>
                        </li>
                        {% if user.is_staff %}
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/">
                                {% if LANGUAGE_CODE == 'ar' %}الإدارة{% else %}Admin{% endif %}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'users:pending_users' %}">
                                {% if LANGUAGE_CODE == 'ar' %}طلبات التسجيل{% else %}Pending Users{% endif %}
                            </a>
                        </li>
                        {% endif %}
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <div class="profile-avatar me-2">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                        {{ user.arabic_name.0|default:user.username.0|upper }}
                                    {% else %}
                                        {{ user.english_name.0|default:user.username.0|upper }}
                                    {% endif %}
                                </div>
                                {% if LANGUAGE_CODE == 'ar' %}
                                    {{ user.arabic_name|default:user.username }}
                                {% else %}
                                    {{ user.english_name|default:user.username }}
                                {% endif %}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'users:profile' %}">
                                    {% if LANGUAGE_CODE == 'ar' %}الملف الشخصي{% else %}Profile{% endif %}
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'users:logout' %}">
                                    {% if LANGUAGE_CODE == 'ar' %}تسجيل الخروج{% else %}Logout{% endif %}
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'users:login' %}">
                                {% if LANGUAGE_CODE == 'ar' %}تسجيل الدخول{% else %}Login{% endif %}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'users:register' %}">
                                {% if LANGUAGE_CODE == 'ar' %}تسجيل جديد{% else %}Register{% endif %}
                            </a>
                        </li>
                    {% endif %}
                    
                    <!-- Language Switcher -->
                    <li class="nav-item">
                        <div class="language-switcher">
                            {% if LANGUAGE_CODE == 'ar' %}
                                <a href="?lang=en" class="btn btn-outline-primary btn-sm fw-bold"
                                   style="min-width: 40px; border-radius: 20px;" title="Switch to English">E</a>
                            {% else %}
                                <a href="?lang=ar" class="btn btn-outline-primary btn-sm fw-bold"
                                   style="min-width: 40px; border-radius: 20px;" title="التبديل إلى العربية">ع</a>
                            {% endif %}
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mt-4">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">
                        {% if LANGUAGE_CODE == 'ar' %}
                            © 2025 نظام تقييم الموظفين. جميع الحقوق محفوظة.
                        {% else %}
                            © 2025 Employee Rating System. All rights reserved.
                        {% endif %}
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">
                        {% if LANGUAGE_CODE == 'ar' %}
                            الإصدار 2.0 - النسخة المحسنة للمؤسسات
                        {% else %}
                            Version 2.0 - Enhanced Enterprise Edition
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
