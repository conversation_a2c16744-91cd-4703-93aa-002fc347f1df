"""
Management command to set up demo data for the Employee Rating System.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.hashers import make_password
from django.db import transaction
from departments.models import Department
from users.models import CustomUser
from evaluations.models import EvaluationCategory, EvaluationQuestion, Evaluation


class Command(BaseCommand):
    help = 'Set up demo data for the Employee Rating System'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset all demo data before creating new data',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('Resetting demo data...')
            self.reset_demo_data()

        self.stdout.write('Creating demo data...')
        
        with transaction.atomic():
            # Create departments
            self.create_departments()
            
            # Create users
            self.create_users()
            
            # Assign users to departments
            self.assign_users_to_departments()
            
            # Create sample evaluations
            self.create_sample_evaluations()

        self.stdout.write(
            self.style.SUCCESS('Successfully created demo data!')
        )
        
        # Print login credentials
        self.print_login_credentials()

    def reset_demo_data(self):
        """Reset all demo data."""
        # Delete sample users (keep original admin)
        sample_usernames = [
            'superadmin', 'it_manager', 'hr_manager', 'sales_manager',
            'dev_supervisor', 'qa_supervisor', 'hr_supervisor',
            'quality_lead', 'quality_analyst',
            'dev_senior', 'dev_junior', 'qa_tester', 'hr_specialist',
            'sales_rep', 'marketing_specialist'
        ]
        CustomUser.objects.filter(username__in=sample_usernames).delete()

        # Also delete by employee_id pattern
        CustomUser.objects.filter(employee_id__startswith='EMP').delete()

        # Delete sample departments
        Department.objects.all().delete()

        # Delete sample evaluations
        Evaluation.objects.all().delete()

    def create_departments(self):
        """Create organizational hierarchy."""
        self.stdout.write('Creating departments...')
        
        # Create root company
        company = Department.objects.create(
            name_en="TechCorp International",
            name_ar="شركة تك كورب الدولية",
            code="TECHCORP",
            description_en="Main company headquarters",
            description_ar="المقر الرئيسي للشركة"
        )
        
        # Create main divisions
        divisions_data = [
            {
                'name_en': 'Information Technology Division',
                'name_ar': 'قسم تقنية المعلومات',
                'code': 'IT_DIV',
                'description_en': 'Technology infrastructure and software development',
                'description_ar': 'البنية التحتية التقنية وتطوير البرمجيات'
            },
            {
                'name_en': 'Human Resources Division',
                'name_ar': 'قسم الموارد البشرية',
                'code': 'HR_DIV',
                'description_en': 'Employee management and organizational development',
                'description_ar': 'إدارة الموظفين والتطوير التنظيمي'
            },
            {
                'name_en': 'Sales & Marketing Division',
                'name_ar': 'قسم المبيعات والتسويق',
                'code': 'SALES_DIV',
                'description_en': 'Customer acquisition and revenue generation',
                'description_ar': 'اكتساب العملاء وتوليد الإيرادات'
            }
        ]
        
        divisions = {}
        for div_data in divisions_data:
            division = Department.objects.create(parent=company, **div_data)
            divisions[div_data['code']] = division
        
        # Create IT sub-departments
        it_departments = [
            {
                'name_en': 'Software Development',
                'name_ar': 'تطوير البرمجيات',
                'code': 'IT_DEV',
                'description_en': 'Application and system development',
                'description_ar': 'تطوير التطبيقات والأنظمة'
            },
            {
                'name_en': 'Quality Assurance',
                'name_ar': 'ضمان الجودة',
                'code': 'IT_QA',
                'description_en': 'Software testing and quality control',
                'description_ar': 'اختبار البرمجيات ومراقبة الجودة'
            }
        ]
        
        for dept_data in it_departments:
            Department.objects.create(parent=divisions['IT_DIV'], **dept_data)
        
        # Create HR sub-departments
        hr_departments = [
            {
                'name_en': 'Recruitment & Talent Acquisition',
                'name_ar': 'التوظيف واكتساب المواهب',
                'code': 'HR_RECRUIT',
                'description_en': 'Employee recruitment and onboarding',
                'description_ar': 'توظيف الموظفين والإعداد'
            }
        ]
        
        for dept_data in hr_departments:
            Department.objects.create(parent=divisions['HR_DIV'], **dept_data)
        
        # Create Sales sub-departments
        sales_departments = [
            {
                'name_en': 'Regional Sales',
                'name_ar': 'المبيعات الإقليمية',
                'code': 'SALES_REG',
                'description_en': 'Regional sales operations',
                'description_ar': 'عمليات المبيعات الإقليمية'
            }
        ]
        
        for dept_data in sales_departments:
            Department.objects.create(parent=divisions['SALES_DIV'], **dept_data)

    def create_users(self):
        """Create sample users."""
        self.stdout.write('Creating users...')
        
        users_data = [
            # Super Admin
            {
                'username': 'superadmin',
                'email': '<EMAIL>',
                'password': 'admin123',
                'employee_id': 'EMP001',
                'english_name': 'System Administrator',
                'arabic_name': 'مدير النظام',
                'role': 'SUPER_ADMIN',
                'is_staff': True,
                'is_superuser': True,
                'phone_number': '******-0001',
                'language_preference': 'en'
            },
            
            # Division Managers
            {
                'username': 'it_manager',
                'email': '<EMAIL>',
                'password': 'manager123',
                'employee_id': 'EMP002',
                'english_name': 'Ahmed Hassan',
                'arabic_name': 'أحمد حسن',
                'role': 'MANAGER',
                'is_staff': True,
                'phone_number': '******-0002',
                'language_preference': 'ar'
            },
            {
                'username': 'hr_manager',
                'email': '<EMAIL>',
                'password': 'manager123',
                'employee_id': 'EMP003',
                'english_name': 'Sarah Johnson',
                'arabic_name': 'سارة جونسون',
                'role': 'MANAGER',
                'is_staff': True,
                'phone_number': '******-0003',
                'language_preference': 'en'
            },
            
            # Department Supervisors
            {
                'username': 'dev_supervisor',
                'email': '<EMAIL>',
                'password': 'supervisor123',
                'employee_id': 'EMP005',
                'english_name': 'Michael Chen',
                'arabic_name': 'مايكل تشين',
                'role': 'SUPERVISOR',
                'phone_number': '******-0005',
                'language_preference': 'en'
            },
            {
                'username': 'qa_supervisor',
                'email': '<EMAIL>',
                'password': 'supervisor123',
                'employee_id': 'EMP006',
                'english_name': 'Fatima Al-Zahra',
                'arabic_name': 'فاطمة الصياحء',
                'role': 'SUPERVISOR',
                'phone_number': '******-0006',
                'language_preference': 'ar'
            },
            
            # Quality Team Members
            {
                'username': 'quality_lead',
                'email': '<EMAIL>',
                'password': 'quality123',
                'employee_id': 'EMP008',
                'english_name': 'Layla Mahmoud',
                'arabic_name': 'ليلى محمود',
                'role': 'QUALITY_TEAM',
                'phone_number': '******-0008',
                'language_preference': 'ar'
            },
            
            # Employees
            {
                'username': 'dev_senior',
                'email': '<EMAIL>',
                'password': 'employee123',
                'employee_id': 'EMP010',
                'english_name': 'Ali Mansour',
                'arabic_name': 'علي منصور',
                'role': 'EMPLOYEE',
                'phone_number': '******-0010',
                'language_preference': 'ar'
            },
            {
                'username': 'dev_junior',
                'email': '<EMAIL>',
                'password': 'employee123',
                'employee_id': 'EMP011',
                'english_name': 'Emma Thompson',
                'arabic_name': 'إيما تومسون',
                'role': 'EMPLOYEE',
                'phone_number': '******-0011',
                'language_preference': 'en'
            },
            {
                'username': 'qa_tester',
                'email': '<EMAIL>',
                'password': 'employee123',
                'employee_id': 'EMP012',
                'english_name': 'Youssef Ibrahim',
                'arabic_name': 'يوسف إبراهيم',
                'role': 'EMPLOYEE',
                'phone_number': '******-0012',
                'language_preference': 'ar'
            }
        ]
        
        # Create users
        self.created_users = {}
        for user_data in users_data:
            password = user_data.pop('password')
            user = CustomUser.objects.create(
                password=make_password(password),
                **user_data
            )
            self.created_users[user.username] = user

    def assign_users_to_departments(self):
        """Assign users to departments."""
        self.stdout.write('Assigning users to departments...')

        # Skip department assignments for now since the relationships are not set up
        # This will be implemented when the department-user relationships are properly configured
        self.stdout.write(self.style.WARNING('Department assignments skipped - will be implemented with proper relationships'))

    def create_sample_evaluations(self):
        """Create sample evaluations for demo."""
        self.stdout.write('Creating sample evaluations...')
        # This will be implemented after the evaluation system is complete
        pass

    def print_login_credentials(self):
        """Print login credentials for demo users."""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.SUCCESS('DEMO LOGIN CREDENTIALS'))
        self.stdout.write('='*60)
        
        credentials = [
            ('Super Admin', 'superadmin', 'admin123', 'Full system access'),
            ('IT Manager', 'it_manager', 'manager123', 'IT Division management'),
            ('HR Manager', 'hr_manager', 'manager123', 'HR Division management'),
            ('Dev Supervisor', 'dev_supervisor', 'supervisor123', 'Development team supervision'),
            ('QA Supervisor', 'qa_supervisor', 'supervisor123', 'QA team supervision'),
            ('Quality Lead', 'quality_lead', 'quality123', 'Quality monitoring (read-only)'),
            ('Senior Developer', 'dev_senior', 'employee123', 'Employee view'),
            ('Junior Developer', 'dev_junior', 'employee123', 'Employee view'),
            ('QA Tester', 'qa_tester', 'employee123', 'Employee view'),
        ]
        
        for role, username, password, description in credentials:
            self.stdout.write(f'{role:15} | {username:15} | {password:12} | {description}')
        
        self.stdout.write('='*60)
        self.stdout.write('Access the system at: http://localhost:8000/users/login/')
        self.stdout.write('='*60)

