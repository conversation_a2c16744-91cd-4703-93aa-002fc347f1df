{% extends 'base.html' %}

{% block title %}
    {% if LANGUAGE_CODE == 'ar' %}
        تفاصيل التقييم - نظام تقييم الموظفين
    {% else %}
        Evaluation Details - Employee Rating System
    {% endif %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h2 text-gray-900 mb-1">
            {% if LANGUAGE_CODE == 'ar' %}
                تفاصيل التقييم
            {% else %}
                Evaluation Details
            {% endif %}
        </h1>
        <p class="text-muted">
            {% if LANGUAGE_CODE == 'ar' %}
                عرض تفاصيل تقييم الموظف
            {% else %}
                View employee evaluation details
            {% endif %}
        </p>
    </div>
    <a href="{% url 'evaluations:list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% if LANGUAGE_CODE == 'ar' %}العودة{% else %}Back{% endif %}
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5>
                    {% if LANGUAGE_CODE == 'ar' %}معلومات التقييم{% else %}Evaluation Information{% endif %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>
                            {% if LANGUAGE_CODE == 'ar' %}الموظف:{% else %}Employee:{% endif %}
                        </strong> {{ evaluation.employee.english_name|default:"N/A" }}</p>
                        <p><strong>
                            {% if LANGUAGE_CODE == 'ar' %}المقيم:{% else %}Evaluator:{% endif %}
                        </strong> {{ evaluation.evaluator.english_name|default:"N/A" }}</p>
                        <p><strong>
                            {% if LANGUAGE_CODE == 'ar' %}فترة التقييم:{% else %}Evaluation Period:{% endif %}
                        </strong> {{ evaluation.evaluation_period_start|date:"M Y"|default:"N/A" }} - {{ evaluation.evaluation_period_end|date:"M Y"|default:"N/A" }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>
                            {% if LANGUAGE_CODE == 'ar' %}الحالة:{% else %}Status:{% endif %}
                        </strong> 
                        {% if evaluation.status == 'DRAFT' %}
                            <span class="badge bg-secondary">
                                {% if LANGUAGE_CODE == 'ar' %}مسودة{% else %}Draft{% endif %}
                            </span>
                        {% elif evaluation.status == 'SUBMITTED' %}
                            <span class="badge bg-warning">
                                {% if LANGUAGE_CODE == 'ar' %}مرسل{% else %}Submitted{% endif %}
                            </span>
                        {% elif evaluation.status == 'APPROVED' %}
                            <span class="badge bg-success">
                                {% if LANGUAGE_CODE == 'ar' %}معتمد{% else %}Approved{% endif %}
                            </span>
                        {% else %}
                            <span class="badge bg-danger">
                                {% if LANGUAGE_CODE == 'ar' %}مرفوض{% else %}Rejected{% endif %}
                            </span>
                        {% endif %}
                        </p>
                        <p><strong>
                            {% if LANGUAGE_CODE == 'ar' %}النتيجة:{% else %}Score:{% endif %}
                        </strong> 
                        {% if evaluation.percentage_score %}
                            {{ evaluation.percentage_score|floatformat:1 }}%
                        {% else %}
                            {% if LANGUAGE_CODE == 'ar' %}غير محسوب{% else %}Not calculated{% endif %}
                        {% endif %}
                        </p>
                        <p><strong>
                            {% if LANGUAGE_CODE == 'ar' %}تاريخ الإنشاء:{% else %}Created:{% endif %}
                        </strong> {{ evaluation.created_at|date:"Y-m-d"|default:"N/A" }}</p>
                    </div>
                </div>
                
                {% if evaluation.comments %}
                <hr>
                <div>
                    <strong>
                        {% if LANGUAGE_CODE == 'ar' %}التعليقات:{% else %}Comments:{% endif %}
                    </strong>
                    <p class="mt-2">{{ evaluation.comments }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>
                    {% if LANGUAGE_CODE == 'ar' %}إجراءات سريعة{% else %}Quick Actions{% endif %}
                </h5>
            </div>
            <div class="card-body">
                {% if user.role in 'SUPER_ADMIN,MANAGER,SUPERVISOR' %}
                <div class="d-grid gap-2">
                    <a href="#" class="btn btn-outline-primary">
                        <i class="fas fa-edit me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}تعديل التقييم{% else %}Edit Evaluation{% endif %}
                    </a>
                    <a href="#" class="btn btn-outline-info">
                        <i class="fas fa-print me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}طباعة التقرير{% else %}Print Report{% endif %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
