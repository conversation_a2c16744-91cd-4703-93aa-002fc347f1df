using System.ComponentModel.DataAnnotations;

namespace EmployeeRatingSystem.Blazor.Models
{
    /// <summary>
    /// Department model with hierarchical structure.
    /// Supports unlimited nesting levels as specified in PRD.
    /// Equivalent to Django's Department model with MPTT.
    /// </summary>
    public class Department : BaseModel
    {
        /// <summary>
        /// Department name in English
        /// </summary>
        [Required]
        [StringLength(255)]
        [Display(Name = "English Name")]
        public string NameEn { get; set; } = string.Empty;

        /// <summary>
        /// Department name in Arabic
        /// </summary>
        [Required]
        [StringLength(255)]
        [Display(Name = "Arabic Name (الاسم بالعربية)")]
        public string NameAr { get; set; } = string.Empty;

        /// <summary>
        /// Unique department identifier code
        /// </summary>
        [Required]
        [StringLength(50)]
        [Display(Name = "Department Code")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Parent department ID for hierarchical structure
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// Parent department navigation property
        /// </summary>
        public virtual Department? Parent { get; set; }

        /// <summary>
        /// Child departments
        /// </summary>
        public virtual ICollection<Department> Children { get; set; } = new List<Department>();

        /// <summary>
        /// Department description in English
        /// </summary>
        [Display(Name = "English Description")]
        public string DescriptionEn { get; set; } = string.Empty;

        /// <summary>
        /// Department description in Arabic
        /// </summary>
        [Display(Name = "Arabic Description (الوصف بالعربية)")]
        public string DescriptionAr { get; set; } = string.Empty;

        /// <summary>
        /// Whether this department is currently active
        /// </summary>
        [Display(Name = "Active")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Department managers (many-to-many)
        /// </summary>
        public virtual ICollection<ApplicationUser> Managers { get; set; } = new List<ApplicationUser>();

        /// <summary>
        /// Users who have this as their primary department
        /// </summary>
        public virtual ICollection<ApplicationUser> PrimaryUsers { get; set; } = new List<ApplicationUser>();

        /// <summary>
        /// All user-department relationships
        /// </summary>
        public virtual ICollection<UserDepartment> UserDepartments { get; set; } = new List<UserDepartment>();

        /// <summary>
        /// Hierarchy level (0 for root departments)
        /// </summary>
        public int Level { get; set; } = 0;

        /// <summary>
        /// Left value for nested set model (for efficient tree queries)
        /// </summary>
        public int Lft { get; set; } = 0;

        /// <summary>
        /// Right value for nested set model (for efficient tree queries)
        /// </summary>
        public int Rght { get; set; } = 0;

        /// <summary>
        /// Tree ID for nested set model
        /// </summary>
        public int TreeId { get; set; } = 0;

        /// <summary>
        /// Get display name based on language preference
        /// </summary>
        public string GetDisplayName(string language = "en")
        {
            return language == "ar" ? NameAr : NameEn;
        }

        /// <summary>
        /// Get description based on language preference
        /// </summary>
        public string GetDescription(string language = "en")
        {
            return language == "ar" ? DescriptionAr : DescriptionEn;
        }

        /// <summary>
        /// Get full path from root to this department
        /// </summary>
        public string GetFullPath(string language = "en", string separator = " > ")
        {
            var path = new List<string>();
            var current = this;
            
            while (current != null)
            {
                path.Insert(0, current.GetDisplayName(language));
                current = current.Parent;
            }
            
            return string.Join(separator, path);
        }

        /// <summary>
        /// Get all ancestor departments
        /// </summary>
        public IEnumerable<Department> GetAncestors()
        {
            var ancestors = new List<Department>();
            var current = Parent;
            
            while (current != null)
            {
                ancestors.Add(current);
                current = current.Parent;
            }
            
            return ancestors;
        }

        /// <summary>
        /// Get all descendant departments
        /// </summary>
        public IEnumerable<Department> GetDescendants()
        {
            var descendants = new List<Department>();
            
            foreach (var child in Children)
            {
                descendants.Add(child);
                descendants.AddRange(child.GetDescendants());
            }
            
            return descendants;
        }

        /// <summary>
        /// Check if this department is an ancestor of the given department
        /// </summary>
        public bool IsAncestorOf(Department department)
        {
            return department.GetAncestors().Contains(this);
        }

        /// <summary>
        /// Check if this department is a descendant of the given department
        /// </summary>
        public bool IsDescendantOf(Department department)
        {
            return GetAncestors().Contains(department);
        }

        /// <summary>
        /// Get employee count for this department only
        /// </summary>
        public int GetDirectEmployeeCount()
        {
            return PrimaryUsers.Count(u => !u.IsDeleted);
        }

        /// <summary>
        /// Get total employee count including sub-departments
        /// </summary>
        public int GetTotalEmployeeCount()
        {
            var count = GetDirectEmployeeCount();
            
            foreach (var child in Children.Where(c => c.IsActive && !c.IsDeleted))
            {
                count += child.GetTotalEmployeeCount();
            }
            
            return count;
        }

        public override string ToString()
        {
            return $"{NameEn} ({Code})";
        }
    }
}
