"""
Core views for Employee Rating System.
"""

from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.utils import translation
from django.http import JsonResponse
from .middleware import LanguageSwitchView


@login_required
def dashboard(request):
    """
    Main dashboard view that redirects users based on their role.
    """
    user = request.user

    # Import models here to avoid circular imports
    from departments.models import Department
    from evaluations.models import EvaluationCategory, Evaluation
    from users.models import CustomUser

    # Get dashboard data
    context = {
        'total_departments': Department.objects.count(),
        'total_users': CustomUser.objects.filter(is_active=True).count(),
        'total_evaluations': Evaluation.objects.count() if hasattr(Evaluation, 'objects') else 0,
        'total_categories': EvaluationCategory.objects.count(),
        'pending_users': CustomUser.objects.filter(is_active=False).count(),
    }

    # Redirect based on user role
    if user.role == 'SUPER_ADMIN':
        return render(request, 'core/super_admin_dashboard.html', context)
    elif user.role == 'MANAGER':
        # Add manager-specific data
        context.update({
            'my_departments': 0,  # Will be updated when department relationships are implemented
            'my_employees': 0,
            'pending_evaluations': 0,
        })
        return render(request, 'core/manager_dashboard.html', context)
    elif user.role == 'SUPERVISOR':
        # Add supervisor-specific data
        context.update({
            'team_members': 0,  # Will be updated when department relationships are implemented
            'pending_evaluations': 0,
            'completed_evaluations': 0,
        })
        return render(request, 'core/supervisor_dashboard.html', context)
    elif user.role == 'QUALITY_TEAM':
        # Add quality team-specific data
        context.update({
            'completed_evaluations': 0,
            'quality_score': 0,
        })
        return render(request, 'core/quality_dashboard.html', context)
    else:  # EMPLOYEE
        # Add employee-specific data
        context.update({
            'latest_score': 0,  # Will be updated when evaluations are implemented
        })
        return render(request, 'core/employee_dashboard.html', context)


def home(request):
    """
    Home page view.
    """
    if request.user.is_authenticated:
        return dashboard(request)
    return render(request, 'core/home.html')


def switch_language(request):
    """Switch user language preference."""
    return LanguageSwitchView.switch_language(request)
