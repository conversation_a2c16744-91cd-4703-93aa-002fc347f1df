{% extends 'base.html' %}

{% block title %}
    {% if LANGUAGE_CODE == 'ar' %}
        الأقسام - نظام تقييم الموظفين
    {% else %}
        Departments - Employee Rating System
    {% endif %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h2 text-gray-900 mb-1">
            {% if LANGUAGE_CODE == 'ar' %}الأقسام{% else %}Departments{% endif %}
        </h1>
        <p class="text-muted">
            {% if LANGUAGE_CODE == 'ar' %}
                إدارة الهيكل التنظيمي للشركة والأقسام المختلفة
            {% else %}
                Manage company organizational structure and departments
            {% endif %}
        </p>
    </div>

    {% if user.role in 'SUPER_ADMIN,MANAGER' %}
    <div class="d-flex gap-2">
        <a href="{% url 'departments:hierarchy' %}" class="btn btn-outline-primary">
            <i class="fas fa-sitemap me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}الهيكل التنظيمي{% else %}Hierarchy View{% endif %}
        </a>
        <a href="{% url 'departments:create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}إضافة قسم{% else %}Add Department{% endif %}
        </a>
    </div>
    {% endif %}
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon me-3" style="background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);">
                    <i class="fas fa-building text-white"></i>
                </div>
                <div>
                    <div class="h4 mb-0">{{ departments.count }}</div>
                    <small class="text-muted">
                        {% if LANGUAGE_CODE == 'ar' %}إجمالي الأقسام{% else %}Total Departments{% endif %}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon me-3" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                    <i class="fas fa-users text-white"></i>
                </div>
                <div>
                    <div class="h4 mb-0">{{ total_employees|default:0 }}</div>
                    <small class="text-muted">
                        {% if LANGUAGE_CODE == 'ar' %}إجمالي الموظفين{% else %}Total Employees{% endif %}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon me-3" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                    <i class="fas fa-user-tie text-white"></i>
                </div>
                <div>
                    <div class="h4 mb-0">{{ total_managers|default:0 }}</div>
                    <small class="text-muted">
                        {% if LANGUAGE_CODE == 'ar' %}المديرون{% else %}Managers{% endif %}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon me-3" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                    <i class="fas fa-layer-group text-white"></i>
                </div>
                <div>
                    <div class="h4 mb-0">{{ active_departments|default:0 }}</div>
                    <small class="text-muted">
                        {% if LANGUAGE_CODE == 'ar' %}الأقسام النشطة{% else %}Active Departments{% endif %}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Departments Grid -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-building me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}قائمة الأقسام{% else %}Departments List{% endif %}
        </h6>
    </div>
    <div class="card-body">
        {% if departments %}
        <div class="row">
            {% for department in departments %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 department-card">
                    <div class="card-body">
                        <div class="d-flex align-items-start justify-content-between mb-3">
                            <div class="department-icon">
                                <i class="fas fa-building text-primary"></i>
                            </div>
                            {% if department.is_active %}
                                <span class="badge bg-success">
                                    {% if LANGUAGE_CODE == 'ar' %}نشط{% else %}Active{% endif %}
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">
                                    {% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}Inactive{% endif %}
                                </span>
                            {% endif %}
                        </div>

                        <h5 class="card-title mb-2">
                            {% if LANGUAGE_CODE == 'ar' %}
                                {{ department.name_ar|default:department.name_en }}
                            {% else %}
                                {{ department.name_en }}
                            {% endif %}
                        </h5>

                        <p class="card-text text-muted mb-3">
                            {% if LANGUAGE_CODE == 'ar' %}
                                {{ department.description_ar|default:department.description_en|truncatewords:12 }}
                            {% else %}
                                {{ department.description_en|truncatewords:12 }}
                            {% endif %}
                        </p>

                        <div class="department-stats mb-3">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="h6 mb-0 text-primary">{{ department.get_employee_count }}</div>
                                        <small class="text-muted">
                                            {% if LANGUAGE_CODE == 'ar' %}موظفين{% else %}Employees{% endif %}
                                        </small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="h6 mb-0 text-success">{{ department.get_manager_count }}</div>
                                        <small class="text-muted">
                                            {% if LANGUAGE_CODE == 'ar' %}مديرين{% else %}Managers{% endif %}
                                        </small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="h6 mb-0 text-info">{{ department.code }}</div>
                                        <small class="text-muted">
                                            {% if LANGUAGE_CODE == 'ar' %}الكود{% else %}Code{% endif %}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <a href="{% url 'departments:detail' department.pk %}" class="btn btn-outline-primary btn-sm flex-fill">
                                <i class="fas fa-eye me-1"></i>
                                {% if LANGUAGE_CODE == 'ar' %}عرض{% else %}View{% endif %}
                            </a>
                            {% if user.role in 'SUPER_ADMIN,MANAGER' %}
                            <a href="{% url 'departments:edit' department.pk %}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-edit"></i>
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-5">
            <div class="empty-state">
                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد أقسام متاحة{% else %}No departments available{% endif %}
                </h5>
                <p class="text-muted">
                    {% if LANGUAGE_CODE == 'ar' %}
                        ابدأ بإضافة أقسام جديدة لتنظيم هيكل الشركة
                    {% else %}
                        Start by adding new departments to organize your company structure
                    {% endif %}
                </p>
                {% if user.role in 'SUPER_ADMIN,MANAGER' %}
                <a href="{% url 'departments:create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}إضافة قسم جديد{% else %}Add New Department{% endif %}
                </a>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<style>
.department-card {
    transition: transform 0.2s ease-in-out;
}

.department-card:hover {
    transform: translateY(-2px);
}

.department-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.department-icon i {
    color: white !important;
}

.department-stats {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
}

.stat-item {
    padding: 0.25rem;
}

.empty-state {
    max-width: 400px;
    margin: 0 auto;
}
</style>
{% endblock %}
