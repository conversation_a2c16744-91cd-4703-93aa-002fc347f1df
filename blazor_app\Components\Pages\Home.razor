﻿@page "/"
@using System.Globalization

<PageTitle>@GetPageTitle()</PageTitle>

<div class="hero-section text-center py-5">
    <div class="container">
        <h1 class="display-4 fw-bold mb-4">
            @if (IsArabic())
            {
                <text>نظام تقييم الموظفين</text>
            }
            else
            {
                <text>Employee Rating System</text>
            }
        </h1>
        <p class="lead mb-4">
            @if (IsArabic())
            {
                <text>نظام شامل لتقييم أداء الموظفين مع دعم اللغة العربية والإنجليزية</text>
            }
            else
            {
                <text>Comprehensive employee performance evaluation system with Arabic and English support</text>
            }
        </p>
        <div class="d-flex gap-3 justify-content-center">
            <a href="/dashboard" class="btn btn-primary btn-lg">
                <i class="fas fa-tachometer-alt me-2"></i>
                @if (IsArabic())
                {
                    <text>لوحة التحكم</text>
                }
                else
                {
                    <text>Dashboard</text>
                }
            </a>
            <a href="/login" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-sign-in-alt me-2"></i>
                @if (IsArabic())
                {
                    <text>تسجيل الدخول</text>
                }
                else
                {
                    <text>Login</text>
                }
            </a>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="container py-5">
    <div class="row">
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <div class="stat-icon text-white mx-auto mb-3">
                        <i class="fas fa-users"></i>
                    </div>
                    <h5 class="card-title">
                        @if (IsArabic())
                        {
                            <text>إدارة الموظفين</text>
                        }
                        else
                        {
                            <text>Employee Management</text>
                        }
                    </h5>
                    <p class="card-text">
                        @if (IsArabic())
                        {
                            <text>إدارة شاملة للموظفين مع هيكل تنظيمي هرمي</text>
                        }
                        else
                        {
                            <text>Comprehensive employee management with hierarchical organizational structure</text>
                        }
                    </p>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <div class="stat-icon text-white mx-auto mb-3">
                        <i class="fas fa-star"></i>
                    </div>
                    <h5 class="card-title">
                        @if (IsArabic())
                        {
                            <text>تقييم الأداء</text>
                        }
                        else
                        {
                            <text>Performance Evaluation</text>
                        }
                    </h5>
                    <p class="card-text">
                        @if (IsArabic())
                        {
                            <text>نظام تقييم قابل للتخصيص مع معايير متعددة</text>
                        }
                        else
                        {
                            <text>Configurable evaluation system with multiple criteria</text>
                        }
                    </p>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <div class="stat-icon text-white mx-auto mb-3">
                        <i class="fas fa-language"></i>
                    </div>
                    <h5 class="card-title">
                        @if (IsArabic())
                        {
                            <text>دعم ثنائي اللغة</text>
                        }
                        else
                        {
                            <text>Bilingual Support</text>
                        }
                    </h5>
                    <p class="card-text">
                        @if (IsArabic())
                        {
                            <text>دعم كامل للغة العربية والإنجليزية مع تخطيط RTL</text>
                        }
                        else
                        {
                            <text>Full Arabic and English support with RTL layout</text>
                        }
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private string GetCurrentLanguage()
    {
        return CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
    }

    private bool IsArabic()
    {
        return GetCurrentLanguage() == "ar";
    }

    private string GetPageTitle()
    {
        return IsArabic() ? "الرئيسية - نظام تقييم الموظفين" : "Home - Employee Rating System";
    }
}
